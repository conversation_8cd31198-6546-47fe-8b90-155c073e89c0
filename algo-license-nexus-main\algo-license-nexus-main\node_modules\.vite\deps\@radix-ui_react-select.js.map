{"version": 3, "sources": ["../../@radix-ui/react-select/src/Select.tsx", "../../@radix-ui/number/src/number.ts", "../../@radix-ui/react-focus-guards/src/FocusGuards.tsx", "../../@radix-ui/react-focus-scope/src/FocusScope.tsx", "../../@radix-ui/react-use-previous/src/usePrevious.tsx", "../../aria-hidden/dist/es2015/index.js", "../../tslib/tslib.es6.mjs", "../../react-remove-scroll/dist/es2015/Combination.js", "../../react-remove-scroll/dist/es2015/UI.js", "../../react-remove-scroll-bar/dist/es2015/constants.js", "../../use-callback-ref/dist/es2015/assignRef.js", "../../use-callback-ref/dist/es2015/useRef.js", "../../use-callback-ref/dist/es2015/useMergeRef.js", "../../use-sidecar/dist/es2015/hoc.js", "../../use-sidecar/dist/es2015/hook.js", "../../use-sidecar/dist/es2015/medium.js", "../../use-sidecar/dist/es2015/renderProp.js", "../../use-sidecar/dist/es2015/exports.js", "../../react-remove-scroll/dist/es2015/medium.js", "../../react-remove-scroll/dist/es2015/SideEffect.js", "../../react-remove-scroll-bar/dist/es2015/component.js", "../../react-style-singleton/dist/es2015/hook.js", "../../get-nonce/dist/es2015/index.js", "../../react-style-singleton/dist/es2015/singleton.js", "../../react-style-singleton/dist/es2015/component.js", "../../react-remove-scroll-bar/dist/es2015/utils.js", "../../react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../react-remove-scroll/dist/es2015/handleScroll.js", "../../react-remove-scroll/dist/es2015/sidecar.js"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value?: string;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface SelectProps {\n  children?: React.ReactNode;\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <BubbleSelect\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </BubbleSelect>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nconst BubbleSelect = React.forwardRef<HTMLSelectElement, React.ComponentPropsWithoutRef<'select'>>(\n  (props, forwardedRef) => {\n    const { value, ...selectProps } = props;\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current!;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much\n     * as possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange` event.\n     * Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use `VisuallyHidden` rather than `display: \"none\"` because Safari autofill\n     * won't work otherwise.\n     */\n    return (\n      <VisuallyHidden asChild>\n        <select {...selectProps} ref={composedRefs} defaultValue={value} />\n      </VisuallyHidden>\n    );\n  }\n);\n\nBubbleSelect.displayName = 'BubbleSelect';\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide ariaLive elements - https://github.com/theKashey/aria-hidden/issues/10\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live]')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useSidecar } from './hook';\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = useSidecar(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, __assign({}, props)) : null;\n    };\n}\n", "import { useState, useEffect } from 'react';\nimport { env } from './env';\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nexport function useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env.forceCache || (env.isNode && !!options.ssr) || !options.async;\n    var _a = useState(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = useState(null), error = _b[0], setError = _b[1];\n    useEffect(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useState, useCallback, useEffect, useLayoutEffect } from 'react';\nexport function renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = useCallback(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            useLayoutEffect(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, __assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = useState(defaultState.current), state = _b[0], setState = _b[1];\n        useEffect(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        if (target instanceof ShadowRoot) {\n            target = target.host;\n        }\n        else {\n            target = target.parentNode;\n        }\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAuB;AACvB,eAA0B;;;ACD1B,SAAS,MAAM,OAAe,CAAC,KAAK,GAAG,GAA6B;AAClE,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;;;ACFA,YAAuB;AAGvB,IAAI,QAAQ;AAWZ,SAAS,iBAAiB;AAClB,EAAA,gBAAU,MAAM;AACpB,UAAM,aAAa,SAAS,iBAAiB,0BAA0B;AACvE,aAAS,KAAK,sBAAsB,cAAc,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACrF,aAAS,KAAK,sBAAsB,aAAa,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACpF;AAEA,WAAO,MAAM;AACX,UAAI,UAAU,GAAG;AACf,iBAAS,iBAAiB,0BAA0B,EAAE,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;MACvF;AACA;IACF;EACF,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,mBAAmB;AAC1B,QAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,UAAQ,aAAa,0BAA0B,EAAE;AACjD,UAAQ,WAAW;AACnB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,WAAW;AACzB,UAAQ,MAAM,gBAAgB;AAC9B,SAAO;AACT;;;ACvCA,IAAAC,SAAuB;AA2MnB,yBAAA;AAtMJ,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAK;AAQzD,IAAM,mBAAmB;AAgCzB,IAAM,aAAmB,kBAA+C,CAAC,OAAO,iBAAiB;AAC/F,QAAM;IACJ,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,oBAAoB;IACpB,GAAG;EACL,IAAI;AACJ,QAAM,CAAC,WAAW,YAAY,IAAU,gBAA6B,IAAI;AACzE,QAAM,mBAAmB,eAAe,oBAAoB;AAC5D,QAAM,qBAAqB,eAAe,sBAAsB;AAChE,QAAM,wBAA8B,cAA2B,IAAI;AACnE,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,aAAa,IAAI,CAAC;AAE/E,QAAM,aAAmB,cAAO;IAC9B,QAAQ;IACR,QAAQ;AACN,WAAK,SAAS;IAChB;IACA,SAAS;AACP,WAAK,SAAS;IAChB;EACF,CAAC,EAAE;AAGG,EAAA,iBAAU,MAAM;AACpB,QAAI,SAAS;AACX,UAASC,iBAAT,SAAuB,OAAmB;AACxC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,SAAS,MAAM;AACrB,YAAI,UAAU,SAAS,MAAM,GAAG;AAC9B,gCAAsB,UAAU;QAClC,OAAO;AACL,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;QACvD;MACF,GAESC,kBAAT,SAAwB,OAAmB;AACzC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,gBAAgB,MAAM;AAY5B,YAAI,kBAAkB,KAAM;AAI5B,YAAI,CAAC,UAAU,SAAS,aAAa,GAAG;AACtC,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;QACvD;MACF,GAKSC,mBAAT,SAAyB,WAA6B;AACpD,cAAM,iBAAiB,SAAS;AAChC,YAAI,mBAAmB,SAAS,KAAM;AACtC,mBAAW,YAAY,WAAW;AAChC,cAAI,SAAS,aAAa,SAAS,EAAG,OAAM,SAAS;QACvD;MACF;AA1CS,UAAA,gBAAAF,gBAUA,iBAAAC,iBA0BA,kBAAAC;AAQT,eAAS,iBAAiB,WAAWF,cAAa;AAClD,eAAS,iBAAiB,YAAYC,eAAc;AACpD,YAAM,mBAAmB,IAAI,iBAAiBC,gBAAe;AAC7D,UAAI,UAAW,kBAAiB,QAAQ,WAAW,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAErF,aAAO,MAAM;AACX,iBAAS,oBAAoB,WAAWF,cAAa;AACrD,iBAAS,oBAAoB,YAAYC,eAAc;AACvD,yBAAiB,WAAW;MAC9B;IACF;EACF,GAAG,CAAC,SAAS,WAAW,WAAW,MAAM,CAAC;AAEpC,EAAA,iBAAU,MAAM;AACpB,QAAI,WAAW;AACb,uBAAiB,IAAI,UAAU;AAC/B,YAAM,2BAA2B,SAAS;AAC1C,YAAM,sBAAsB,UAAU,SAAS,wBAAwB;AAEvE,UAAI,CAAC,qBAAqB;AACxB,cAAM,aAAa,IAAI,YAAY,oBAAoB,aAAa;AACpE,kBAAU,iBAAiB,oBAAoB,gBAAgB;AAC/D,kBAAU,cAAc,UAAU;AAClC,YAAI,CAAC,WAAW,kBAAkB;AAChC,qBAAW,YAAY,sBAAsB,SAAS,CAAC,GAAG,EAAE,QAAQ,KAAK,CAAC;AAC1E,cAAI,SAAS,kBAAkB,0BAA0B;AACvD,kBAAM,SAAS;UACjB;QACF;MACF;AAEA,aAAO,MAAM;AACX,kBAAU,oBAAoB,oBAAoB,gBAAgB;AAKlE,mBAAW,MAAM;AACf,gBAAM,eAAe,IAAI,YAAY,sBAAsB,aAAa;AACxE,oBAAU,iBAAiB,sBAAsB,kBAAkB;AACnE,oBAAU,cAAc,YAAY;AACpC,cAAI,CAAC,aAAa,kBAAkB;AAClC,kBAAM,4BAA4B,SAAS,MAAM,EAAE,QAAQ,KAAK,CAAC;UACnE;AAEA,oBAAU,oBAAoB,sBAAsB,kBAAkB;AAEtE,2BAAiB,OAAO,UAAU;QACpC,GAAG,CAAC;MACN;IACF;EACF,GAAG,CAAC,WAAW,kBAAkB,oBAAoB,UAAU,CAAC;AAGhE,QAAM,gBAAsB;IAC1B,CAAC,UAA+B;AAC9B,UAAI,CAAC,QAAQ,CAAC,QAAS;AACvB,UAAI,WAAW,OAAQ;AAEvB,YAAM,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,WAAW,CAAC,MAAM;AAClF,YAAM,iBAAiB,SAAS;AAEhC,UAAI,YAAY,gBAAgB;AAC9B,cAAME,aAAY,MAAM;AACxB,cAAM,CAAC,OAAO,IAAI,IAAI,iBAAiBA,UAAS;AAChD,cAAM,4BAA4B,SAAS;AAG3C,YAAI,CAAC,2BAA2B;AAC9B,cAAI,mBAAmBA,WAAW,OAAM,eAAe;QACzD,OAAO;AACL,cAAI,CAAC,MAAM,YAAY,mBAAmB,MAAM;AAC9C,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,OAAO,EAAE,QAAQ,KAAK,CAAC;UACzC,WAAW,MAAM,YAAY,mBAAmB,OAAO;AACrD,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,MAAM,EAAE,QAAQ,KAAK,CAAC;UACxC;QACF;MACF;IACF;IACA,CAAC,MAAM,SAAS,WAAW,MAAM;EACnC;AAEA,aACE,wBAAC,UAAU,KAAV,EAAc,UAAU,IAAK,GAAG,YAAY,KAAK,cAAc,WAAW,cAAA,CAAe;AAE9F,CAAC;AAED,WAAW,cAAc;AAUzB,SAAS,WAAW,YAA2B,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AACtE,QAAM,2BAA2B,SAAS;AAC1C,aAAW,aAAa,YAAY;AAClC,UAAM,WAAW,EAAE,OAAO,CAAC;AAC3B,QAAI,SAAS,kBAAkB,yBAA0B;EAC3D;AACF;AAKA,SAAS,iBAAiB,WAAwB;AAChD,QAAM,aAAa,sBAAsB,SAAS;AAClD,QAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,QAAM,OAAO,YAAY,WAAW,QAAQ,GAAG,SAAS;AACxD,SAAO,CAAC,OAAO,IAAI;AACrB;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAMA,SAAS,YAAY,UAAyB,WAAwB;AACpE,aAAW,WAAW,UAAU;AAE9B,QAAI,CAAC,SAAS,SAAS,EAAE,MAAM,UAAU,CAAC,EAAG,QAAO;EACtD;AACF;AAEA,SAAS,SAAS,MAAmB,EAAE,KAAK,GAA2B;AACrE,MAAI,iBAAiB,IAAI,EAAE,eAAe,SAAU,QAAO;AAC3D,SAAO,MAAM;AAEX,QAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,QAAI,iBAAiB,IAAI,EAAE,YAAY,OAAQ,QAAO;AACtD,WAAO,KAAK;EACd;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,SAAmE;AAC5F,SAAO,mBAAmB,oBAAoB,YAAY;AAC5D;AAEA,SAAS,MAAM,SAAkC,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AAExE,MAAI,WAAW,QAAQ,OAAO;AAC5B,UAAM,2BAA2B,SAAS;AAE1C,YAAQ,MAAM,EAAE,eAAe,KAAK,CAAC;AAErC,QAAI,YAAY,4BAA4B,kBAAkB,OAAO,KAAK;AACxE,cAAQ,OAAO;EACnB;AACF;AAOA,IAAM,mBAAmB,uBAAuB;AAEhD,SAAS,yBAAyB;AAEhC,MAAI,QAAyB,CAAC;AAE9B,SAAO;IACL,IAAI,YAA2B;AAE7B,YAAM,mBAAmB,MAAM,CAAC;AAChC,UAAI,eAAe,kBAAkB;AACnC,6DAAkB;MACpB;AAEA,cAAQ,YAAY,OAAO,UAAU;AACrC,YAAM,QAAQ,UAAU;IAC1B;IAEA,OAAO,YAA2B;;AAChC,cAAQ,YAAY,OAAO,UAAU;AACrC,kBAAM,CAAC,MAAP,mBAAU;IACZ;EACF;AACF;AAEA,SAAS,YAAe,OAAY,MAAS;AAC3C,QAAM,eAAe,CAAC,GAAG,KAAK;AAC9B,QAAM,QAAQ,aAAa,QAAQ,IAAI;AACvC,MAAI,UAAU,IAAI;AAChB,iBAAa,OAAO,OAAO,CAAC;EAC9B;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAsB;AACzC,SAAO,MAAM,OAAO,CAAC,SAAS,KAAK,YAAY,GAAG;AACpD;;;ACtVA,IAAAC,SAAuB;AAEvB,SAAS,YAAe,OAAU;AAChC,QAAM,MAAY,cAAO,EAAE,OAAO,UAAU,MAAM,CAAC;AAKnD,SAAa,eAAQ,MAAM;AACzB,QAAI,IAAI,QAAQ,UAAU,OAAO;AAC/B,UAAI,QAAQ,WAAW,IAAI,QAAQ;AACnC,UAAI,QAAQ,QAAQ;IACtB;AACA,WAAO,IAAI,QAAQ;EACrB,GAAG,CAAC,KAAK,CAAC;AACZ;;;ACfA,IAAI,mBAAmB,SAAU,gBAAgB;AAC7C,MAAI,OAAO,aAAa,aAAa;AACjC,WAAO;AAAA,EACX;AACA,MAAI,eAAe,MAAM,QAAQ,cAAc,IAAI,eAAe,CAAC,IAAI;AACvE,SAAO,aAAa,cAAc;AACtC;AACA,IAAI,aAAa,oBAAI,QAAQ;AAC7B,IAAI,oBAAoB,oBAAI,QAAQ;AACpC,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa,SAAU,MAAM;AAC7B,SAAO,SAAS,KAAK,QAAQ,WAAW,KAAK,UAAU;AAC3D;AACA,IAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC5C,SAAO,QACF,IAAI,SAAU,QAAQ;AACvB,QAAI,OAAO,SAAS,MAAM,GAAG;AACzB,aAAO;AAAA,IACX;AACA,QAAI,kBAAkB,WAAW,MAAM;AACvC,QAAI,mBAAmB,OAAO,SAAS,eAAe,GAAG;AACrD,aAAO;AAAA,IACX;AACA,YAAQ,MAAM,eAAe,QAAQ,2BAA2B,QAAQ,iBAAiB;AACzF,WAAO;AAAA,EACX,CAAC,EACI,OAAO,SAAU,GAAG;AAAE,WAAO,QAAQ,CAAC;AAAA,EAAG,CAAC;AACnD;AASA,IAAI,yBAAyB,SAAU,gBAAgB,YAAY,YAAY,kBAAkB;AAC7F,MAAI,UAAU,eAAe,YAAY,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1G,MAAI,CAAC,UAAU,UAAU,GAAG;AACxB,cAAU,UAAU,IAAI,oBAAI,QAAQ;AAAA,EACxC;AACA,MAAI,gBAAgB,UAAU,UAAU;AACxC,MAAI,cAAc,CAAC;AACnB,MAAI,iBAAiB,oBAAI,IAAI;AAC7B,MAAI,iBAAiB,IAAI,IAAI,OAAO;AACpC,MAAI,OAAO,SAAU,IAAI;AACrB,QAAI,CAAC,MAAM,eAAe,IAAI,EAAE,GAAG;AAC/B;AAAA,IACJ;AACA,mBAAe,IAAI,EAAE;AACrB,SAAK,GAAG,UAAU;AAAA,EACtB;AACA,UAAQ,QAAQ,IAAI;AACpB,MAAI,OAAO,SAAU,QAAQ;AACzB,QAAI,CAAC,UAAU,eAAe,IAAI,MAAM,GAAG;AACvC;AAAA,IACJ;AACA,UAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAU,MAAM;AAC1D,UAAI,eAAe,IAAI,IAAI,GAAG;AAC1B,aAAK,IAAI;AAAA,MACb,OACK;AACD,YAAI;AACA,cAAI,OAAO,KAAK,aAAa,gBAAgB;AAC7C,cAAI,gBAAgB,SAAS,QAAQ,SAAS;AAC9C,cAAI,gBAAgB,WAAW,IAAI,IAAI,KAAK,KAAK;AACjD,cAAI,eAAe,cAAc,IAAI,IAAI,KAAK,KAAK;AACnD,qBAAW,IAAI,MAAM,YAAY;AACjC,wBAAc,IAAI,MAAM,WAAW;AACnC,sBAAY,KAAK,IAAI;AACrB,cAAI,iBAAiB,KAAK,eAAe;AACrC,8BAAkB,IAAI,MAAM,IAAI;AAAA,UACpC;AACA,cAAI,gBAAgB,GAAG;AACnB,iBAAK,aAAa,YAAY,MAAM;AAAA,UACxC;AACA,cAAI,CAAC,eAAe;AAChB,iBAAK,aAAa,kBAAkB,MAAM;AAAA,UAC9C;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,mCAAmC,MAAM,CAAC;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,OAAK,UAAU;AACf,iBAAe,MAAM;AACrB;AACA,SAAO,WAAY;AACf,gBAAY,QAAQ,SAAU,MAAM;AAChC,UAAI,eAAe,WAAW,IAAI,IAAI,IAAI;AAC1C,UAAI,cAAc,cAAc,IAAI,IAAI,IAAI;AAC5C,iBAAW,IAAI,MAAM,YAAY;AACjC,oBAAc,IAAI,MAAM,WAAW;AACnC,UAAI,CAAC,cAAc;AACf,YAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG;AAC9B,eAAK,gBAAgB,gBAAgB;AAAA,QACzC;AACA,0BAAkB,OAAO,IAAI;AAAA,MACjC;AACA,UAAI,CAAC,aAAa;AACd,aAAK,gBAAgB,UAAU;AAAA,MACnC;AAAA,IACJ,CAAC;AACD;AACA,QAAI,CAAC,WAAW;AAEZ,mBAAa,oBAAI,QAAQ;AACzB,mBAAa,oBAAI,QAAQ;AACzB,0BAAoB,oBAAI,QAAQ;AAChC,kBAAY,CAAC;AAAA,IACjB;AAAA,EACJ;AACJ;AAQO,IAAI,aAAa,SAAU,gBAAgB,YAAY,YAAY;AACtE,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAoB;AAC9D,MAAI,UAAU,MAAM,KAAK,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1F,MAAI,mBAAmB,cAAc,iBAAiB,cAAc;AACpE,MAAI,CAAC,kBAAkB;AACnB,WAAO,WAAY;AAAE,aAAO;AAAA,IAAM;AAAA,EACtC;AAEA,UAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,iBAAiB,iBAAiB,aAAa,CAAC,CAAC;AACxF,SAAO,uBAAuB,SAAS,kBAAkB,YAAY,aAAa;AACtF;;;ACtGO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC5NA,IAAAC,UAAuB;;;ACAvB,IAAAC,SAAuB;;;ACDhB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;AAK5B,IAAI,yBAAyB;;;ACM7B,SAAS,UAAU,KAAK,OAAO;AAClC,MAAI,OAAO,QAAQ,YAAY;AAC3B,QAAI,KAAK;AAAA,EACb,WACS,KAAK;AACV,QAAI,UAAU;AAAA,EAClB;AACA,SAAO;AACX;;;ACrBA,mBAAyB;AAelB,SAASC,gBAAe,cAAc,UAAU;AACnD,MAAI,UAAM,uBAAS,WAAY;AAAE,WAAQ;AAAA;AAAA,MAErC,OAAO;AAAA;AAAA,MAEP;AAAA;AAAA,MAEA,QAAQ;AAAA,QACJ,IAAI,UAAU;AACV,iBAAO,IAAI;AAAA,QACf;AAAA,QACA,IAAI,QAAQ,OAAO;AACf,cAAI,OAAO,IAAI;AACf,cAAI,SAAS,OAAO;AAChB,gBAAI,QAAQ;AACZ,gBAAI,SAAS,OAAO,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EAAI,CAAC,EAAE,CAAC;AAER,MAAI,WAAW;AACf,SAAO,IAAI;AACf;;;ACtCA,IAAAC,SAAuB;AAGvB,IAAI,4BAA4B,OAAO,WAAW,cAAoB,yBAAwB;AAC9F,IAAI,gBAAgB,oBAAI,QAAQ;AAezB,SAAS,aAAa,MAAM,cAAc;AAC7C,MAAI,cAAcC,gBAAe,gBAAgB,MAAM,SAAU,UAAU;AACvE,WAAO,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,UAAU,KAAK,QAAQ;AAAA,IAAG,CAAC;AAAA,EAC3E,CAAC;AAED,4BAA0B,WAAY;AAClC,QAAI,WAAW,cAAc,IAAI,WAAW;AAC5C,QAAI,UAAU;AACV,UAAI,aAAa,IAAI,IAAI,QAAQ;AACjC,UAAI,aAAa,IAAI,IAAI,IAAI;AAC7B,UAAI,YAAY,YAAY;AAC5B,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,IAAI;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,SAAS;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL;AACA,kBAAc,IAAI,aAAa,IAAI;AAAA,EACvC,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACX;;;AC3CA,IAAAC,SAAuB;;;ACDvB,IAAAC,gBAAoC;;;ACCpC,SAAS,KAAK,GAAG;AACb,SAAO;AACX;AACA,SAAS,kBAAkB,UAAU,YAAY;AAC7C,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAM;AAChD,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AACf,MAAI,SAAS;AAAA,IACT,MAAM,WAAY;AACd,UAAI,UAAU;AACV,cAAM,IAAI,MAAM,kGAAkG;AAAA,MACtH;AACA,UAAI,OAAO,QAAQ;AACf,eAAO,OAAO,OAAO,SAAS,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAAA,IACA,WAAW,SAAU,MAAM;AACvB,UAAI,OAAO,WAAW,MAAM,QAAQ;AACpC,aAAO,KAAK,IAAI;AAChB,aAAO,WAAY;AACf,iBAAS,OAAO,OAAO,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAM,CAAC;AAAA,MAC9D;AAAA,IACJ;AAAA,IACA,kBAAkB,SAAU,IAAI;AAC5B,iBAAW;AACX,aAAO,OAAO,QAAQ;AAClB,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AAAA,MAClB;AACA,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AAAE,iBAAO,GAAG,CAAC;AAAA,QAAG;AAAA,QACnC,QAAQ,WAAY;AAAE,iBAAO;AAAA,QAAQ;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,cAAc,SAAU,IAAI;AACxB,iBAAW;AACX,UAAI,eAAe,CAAC;AACpB,UAAI,OAAO,QAAQ;AACf,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AACd,uBAAe;AAAA,MACnB;AACA,UAAI,eAAe,WAAY;AAC3B,YAAIC,OAAM;AACV,uBAAe,CAAC;AAChB,QAAAA,KAAI,QAAQ,EAAE;AAAA,MAClB;AACA,UAAI,QAAQ,WAAY;AAAE,eAAO,QAAQ,QAAQ,EAAE,KAAK,YAAY;AAAA,MAAG;AACvE,YAAM;AACN,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AACf,uBAAa,KAAK,CAAC;AACnB,gBAAM;AAAA,QACV;AAAA,QACA,QAAQ,SAAU,QAAQ;AACtB,yBAAe,aAAa,OAAO,MAAM;AACzC,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,oBAAoB,SAAS;AACzC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,kBAAkB,IAAI;AACnC,SAAO,UAAU,SAAS,EAAE,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO;AAC9D,SAAO;AACX;;;AC5EA,IAAAC,SAAuB;AACvB,IAAAC,gBAAkE;;;ACDlE,IAAAC,SAAuB;AACvB,IAAI,UAAU,SAAU,IAAI;AACxB,MAAI,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;AACvD,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACxF;AACA,MAAI,SAAS,QAAQ,KAAK;AAC1B,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,SAAa,qBAAc,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC;AACzD;AACA,QAAQ,kBAAkB;AACnB,SAAS,cAAc,QAAQ,UAAU;AAC5C,SAAO,UAAU,QAAQ;AACzB,SAAO;AACX;;;AChBO,IAAI,YAAY,oBAAoB;;;AVI3C,IAAI,UAAU,WAAY;AACtB;AACJ;AAIA,IAAI,eAAqB,kBAAW,SAAU,OAAO,WAAW;AAC5D,MAAI,MAAY,cAAO,IAAI;AAC3B,MAAI,KAAW,gBAAS;AAAA,IACpB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACxB,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,MAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,OAAO,SAAS,QAAQ,IAAI,UAAU,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC,gBAAgB,YAAY,aAAa,mBAAmB,WAAW,UAAU,WAAW,eAAe,SAAS,kBAAkB,MAAM,SAAS,CAAC;AAC1iB,MAAIC,WAAU;AACd,MAAI,eAAe,aAAa,CAAC,KAAK,SAAS,CAAC;AAChD,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,SAAS;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAkB,qBAAcA,UAAS,EAAE,SAAS,WAAW,iBAAkC,QAAgB,aAA0B,OAAc,cAA4B,gBAAgB,CAAC,CAAC,gBAAgB,SAAS,KAAK,QAAiB,CAAC;AAAA,IACvP,eAAsB,oBAAmB,gBAAS,KAAK,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,KAAK,aAAa,CAAC,CAAC,IAAY,qBAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,EAAE,WAAsB,KAAK,aAAa,CAAC,GAAG,QAAQ;AAAA,EAAE;AACjQ,CAAC;AACD,aAAa,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AACX;AACA,aAAa,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,WAAW;AACf;;;AWjCA,IAAAC,UAAuB;;;ACDvB,IAAAC,UAAuB;;;ACAvB,IAAAC,SAAuB;;;ACAvB,IAAI;AAIG,IAAI,WAAW,WAAY;AAC9B,MAAI,cAAc;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,sBAAsB,aAAa;AAC1C,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACXA,SAAS,eAAe;AACpB,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,SAAS,cAAc,OAAO;AACxC,MAAI,OAAO;AACX,MAAI,QAAQ,SAAS;AACrB,MAAI,OAAO;AACP,QAAI,aAAa,SAAS,KAAK;AAAA,EACnC;AACA,SAAO;AACX;AACA,SAAS,aAAa,KAAK,KAAK;AAE5B,MAAI,IAAI,YAAY;AAEhB,QAAI,WAAW,UAAU;AAAA,EAC7B,OACK;AACD,QAAI,YAAY,SAAS,eAAe,GAAG,CAAC;AAAA,EAChD;AACJ;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,OAAK,YAAY,GAAG;AACxB;AACO,IAAI,sBAAsB,WAAY;AACzC,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,SAAO;AAAA,IACH,KAAK,SAAU,OAAO;AAClB,UAAI,WAAW,GAAG;AACd,YAAK,aAAa,aAAa,GAAI;AAC/B,uBAAa,YAAY,KAAK;AAC9B,yBAAe,UAAU;AAAA,QAC7B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,IACA,QAAQ,WAAY;AAChB;AACA,UAAI,CAAC,WAAW,YAAY;AACxB,mBAAW,cAAc,WAAW,WAAW,YAAY,UAAU;AACrE,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACJ;;;AFpCO,IAAI,qBAAqB,WAAY;AACxC,MAAI,QAAQ,oBAAoB;AAChC,SAAO,SAAU,QAAQ,WAAW;AAChC,IAAM,iBAAU,WAAY;AACxB,YAAM,IAAI,MAAM;AAChB,aAAO,WAAY;AACf,cAAM,OAAO;AAAA,MACjB;AAAA,IACJ,GAAG,CAAC,UAAU,SAAS,CAAC;AAAA,EAC5B;AACJ;;;AGdO,IAAI,iBAAiB,WAAY;AACpC,MAAI,WAAW,mBAAmB;AAClC,MAAI,QAAQ,SAAU,IAAI;AACtB,QAAI,SAAS,GAAG,QAAQ,UAAU,GAAG;AACrC,aAAS,QAAQ,OAAO;AACxB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACfO,IAAI,UAAU;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAI,QAAQ,SAAU,GAAG;AAAE,SAAO,SAAS,KAAK,IAAI,EAAE,KAAK;AAAG;AAC9D,IAAI,YAAY,SAAU,SAAS;AAC/B,MAAI,KAAK,OAAO,iBAAiB,SAAS,IAAI;AAC9C,MAAI,OAAO,GAAG,YAAY,YAAY,gBAAgB,YAAY;AAClE,MAAI,MAAM,GAAG,YAAY,YAAY,eAAe,WAAW;AAC/D,MAAI,QAAQ,GAAG,YAAY,YAAY,iBAAiB,aAAa;AACrE,SAAO,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC;AACjD;AACO,IAAI,cAAc,SAAU,SAAS;AACxC,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,UAAU,UAAU,OAAO;AAC/B,MAAI,gBAAgB,SAAS,gBAAgB;AAC7C,MAAI,cAAc,OAAO;AACzB,SAAO;AAAA,IACH,MAAM,QAAQ,CAAC;AAAA,IACf,KAAK,QAAQ,CAAC;AAAA,IACd,OAAO,QAAQ,CAAC;AAAA,IAChB,KAAK,KAAK,IAAI,GAAG,cAAc,gBAAgB,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC;AAAA,EAC1E;AACJ;;;ALxBA,IAAI,QAAQ,eAAe;AACpB,IAAI,gBAAgB;AAI3B,IAAI,YAAY,SAAU,IAAI,eAAe,SAAS,WAAW;AAC7D,MAAI,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,MAAM,GAAG;AAC7D,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,SAAO,QAAQ,OAAO,uBAAuB,0BAA0B,EAAE,OAAO,WAAW,uBAAuB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,eAAe,4BAA4B,EAAE,OAAO,WAAW,4CAA4C,EAAE,OAAO;AAAA,IACnS,iBAAiB,sBAAsB,OAAO,WAAW,GAAG;AAAA,IAC5D,YAAY,YACR,uBAAuB,OAAO,MAAM,wBAAwB,EAAE,OAAO,KAAK,0BAA0B,EAAE,OAAO,OAAO,gEAAgE,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,SAAS;AAAA,IACxO,YAAY,aAAa,kBAAkB,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,GAAG;AAAA,EACvF,EACK,OAAO,OAAO,EACd,KAAK,EAAE,GAAG,gBAAgB,EAAE,OAAO,oBAAoB,iBAAiB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,wBAAwB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,mBAAmB,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,0BAA0B,EAAE,OAAO,WAAW,qBAAqB,EAAE,OAAO,eAAe,WAAW,EAAE,OAAO,wBAAwB,IAAI,EAAE,OAAO,KAAK,YAAY;AAC/kB;AACA,IAAI,uBAAuB,WAAY;AACnC,MAAI,UAAU,SAAS,SAAS,KAAK,aAAa,aAAa,KAAK,KAAK,EAAE;AAC3E,SAAO,SAAS,OAAO,IAAI,UAAU;AACzC;AACO,IAAI,mBAAmB,WAAY;AACtC,EAAM,kBAAU,WAAY;AACxB,aAAS,KAAK,aAAa,gBAAgB,qBAAqB,IAAI,GAAG,SAAS,CAAC;AACjF,WAAO,WAAY;AACf,UAAI,aAAa,qBAAqB,IAAI;AAC1C,UAAI,cAAc,GAAG;AACjB,iBAAS,KAAK,gBAAgB,aAAa;AAAA,MAC/C,OACK;AACD,iBAAS,KAAK,aAAa,eAAe,WAAW,SAAS,CAAC;AAAA,MACnE;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AAIO,IAAI,kBAAkB,SAAU,IAAI;AACvC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,WAAW;AACpH,mBAAiB;AAMjB,MAAI,MAAY,gBAAQ,WAAY;AAAE,WAAO,YAAY,OAAO;AAAA,EAAG,GAAG,CAAC,OAAO,CAAC;AAC/E,SAAa,sBAAc,OAAO,EAAE,QAAQ,UAAU,KAAK,CAAC,YAAY,SAAS,CAAC,cAAc,eAAe,EAAE,EAAE,CAAC;AACxH;;;AMpDA,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC/B,MAAI;AACI,cAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC/C,KAAK,WAAY;AACb,2BAAmB;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAED,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAEhD,WAAO,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACvD,SACO,KAAK;AACR,uBAAmB;AAAA,EACvB;AACJ;AAdY;AAeL,IAAI,aAAa,mBAAmB,EAAE,SAAS,MAAM,IAAI;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,SAAO,KAAK,YAAY;AAC5B;AACA,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,MAAI,EAAE,gBAAgB,UAAU;AAC5B,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO,iBAAiB,IAAI;AACzC;AAAA;AAAA,IAEA,OAAO,QAAQ,MAAM;AAAA,IAEjB,EAAE,OAAO,cAAc,OAAO,aAAa,CAAC,qBAAqB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AACvG;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AAChG,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AACzF,IAAI,0BAA0B,SAAU,MAAM,MAAM;AACvD,MAAI,gBAAgB,KAAK;AACzB,MAAI,UAAU;AACd,KAAG;AAEC,QAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACpE,gBAAU,QAAQ;AAAA,IACtB;AACA,QAAI,eAAe,uBAAuB,MAAM,OAAO;AACvD,QAAI,cAAc;AACd,UAAI,KAAK,mBAAmB,MAAM,OAAO,GAAG,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AACrF,UAAI,eAAe,cAAc;AAC7B,eAAO;AAAA,MACX;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB,SAAS,WAAW,YAAY,cAAc;AAC9C,SAAO;AACX;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,SAAO,SAAS,MAAM,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AACtF;AACA,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,SAAO,SAAS,MAAM,oBAAoB,IAAI,IAAI,oBAAoB,IAAI;AAC9E;AACA,IAAI,qBAAqB,SAAU,MAAM,WAAW;AAMhD,SAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACtD;AACO,IAAI,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;AACnF,MAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,SAAS,EAAE,SAAS;AAC3F,MAAI,QAAQ,kBAAkB;AAE9B,MAAI,SAAS,MAAM;AACnB,MAAI,eAAe,UAAU,SAAS,MAAM;AAC5C,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AACzB,KAAG;AACC,QAAI,KAAK,mBAAmB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9F,QAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,QAAI,YAAY,eAAe;AAC3B,UAAI,uBAAuB,MAAM,MAAM,GAAG;AACtC,2BAAmB;AACnB,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,kBAAkB,YAAY;AAC9B,eAAS,OAAO;AAAA,IACpB,OACK;AACD,eAAS,OAAO;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA,IAEC,CAAC,gBAAgB,WAAW,SAAS;AAAA,IAEjC,iBAAiB,UAAU,SAAS,MAAM,KAAK,cAAc;AAAA;AAElE,MAAI,oBACE,gBAAgB,KAAK,IAAI,eAAe,IAAI,KAAO,CAAC,gBAAgB,QAAQ,kBAAmB;AACjG,yBAAqB;AAAA,EACzB,WACS,CAAC,oBACJ,gBAAgB,KAAK,IAAI,kBAAkB,IAAI,KAAO,CAAC,gBAAgB,CAAC,QAAQ,qBAAsB;AACxG,yBAAqB;AAAA,EACzB;AACA,SAAO;AACX;;;ARpGO,IAAI,aAAa,SAAU,OAAO;AACrC,SAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AACjH;AACO,IAAI,aAAa,SAAU,OAAO;AAAE,SAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAG;AAChF,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AACnD;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG;AAC5E,IAAI,gBAAgB,SAAU,IAAI;AAAE,SAAO,4BAA4B,OAAO,IAAI,mDAAmD,EAAE,OAAO,IAAI,2BAA2B;AAAG;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC;AACV,SAAS,oBAAoB,OAAO;AACvC,MAAI,qBAA2B,eAAO,CAAC,CAAC;AACxC,MAAI,gBAAsB,eAAO,CAAC,GAAG,CAAC,CAAC;AACvC,MAAI,aAAmB,eAAO;AAC9B,MAAI,KAAW,iBAAS,WAAW,EAAE,CAAC;AACtC,MAAIC,SAAc,iBAAS,cAAc,EAAE,CAAC;AAC5C,MAAI,YAAkB,eAAO,KAAK;AAClC,EAAM,kBAAU,WAAY;AACxB,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,kBAAU,WAAY;AACxB,QAAI,MAAM,OAAO;AACb,eAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAC7D,UAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU,GAAG,IAAI,EAAE,OAAO,OAAO;AAC/G,cAAQ,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAAA,MAAG,CAAC;AAC7F,aAAO,WAAY;AACf,iBAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAChE,gBAAQ,QAAQ,SAAU,IAAI;AAAE,iBAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAAA,QAAG,CAAC;AAAA,MACpG;AAAA,IACJ;AACA;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,SAAS,MAAM,MAAM,CAAC;AACrD,MAAI,oBAA0B,oBAAY,SAAU,OAAO,QAAQ;AAC/D,QAAK,aAAa,SAAS,MAAM,QAAQ,WAAW,KAAO,MAAM,SAAS,WAAW,MAAM,SAAU;AACjG,aAAO,CAAC,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,QAAQ,WAAW,KAAK;AAC5B,QAAI,aAAa,cAAc;AAC/B,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI;AACJ,QAAI,SAAS,MAAM;AACnB,QAAI,gBAAgB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AAEhE,QAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,SAAS;AACxE,aAAO;AAAA,IACX;AACA,QAAI,+BAA+B,wBAAwB,eAAe,MAAM;AAChF,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,8BAA8B;AAC9B,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc,kBAAkB,MAAM,MAAM;AAC5C,qCAA+B,wBAAwB,eAAe,MAAM;AAAA,IAEhF;AACA,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,WAAW,oBAAoB,UAAU,UAAU,SAAS;AACxE,iBAAW,UAAU;AAAA,IACzB;AACA,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,WAAW,WAAW;AAC1C,WAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,IAAI;AAAA,EACnG,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,oBAAY,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU,UAAU,UAAU,UAAU,SAAS,CAAC,MAAMA,QAAO;AAEhE;AAAA,IACJ;AACA,QAAI,QAAQ,YAAY,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK;AACpE,QAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS,MAAM,SAAS,EAAE,WAAW,MAAM,UAAU,MAAM,WAAW,EAAE,iBAAiB,aAAa,EAAE,OAAO,KAAK;AAAA,IAAG,CAAC,EAAE,CAAC;AAEvM,QAAI,eAAe,YAAY,QAAQ;AACnC,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA;AAAA,IACJ;AAEA,QAAI,CAAC,aAAa;AACd,UAAI,cAAc,UAAU,QAAQ,UAAU,CAAC,GAC1C,IAAI,UAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,SAAS,MAAM,MAAM;AAAA,MAAG,CAAC;AACnE,UAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,QAAQ;AACtG,UAAI,YAAY;AACZ,YAAI,MAAM,YAAY;AAClB,gBAAM,eAAe;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,eAAqB,oBAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;AACxE,QAAI,QAAQ,EAAE,MAAY,OAAc,QAAgB,QAAgB,cAAc,yBAAyB,MAAM,EAAE;AACvH,uBAAmB,QAAQ,KAAK,KAAK;AACrC,eAAW,WAAY;AACnB,yBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IACvG,GAAG,CAAC;AAAA,EACR,GAAG,CAAC,CAAC;AACL,MAAI,mBAAyB,oBAAY,SAAU,OAAO;AACtD,kBAAc,UAAU,WAAW,KAAK;AACxC,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,cAAoB,oBAAY,SAAU,OAAO;AACjD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,MAAI,kBAAwB,oBAAY,SAAU,OAAO;AACrD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,EAAM,kBAAU,WAAY;AACxB,cAAU,KAAKA,MAAK;AACpB,UAAM,aAAa;AAAA,MACf,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACxB,CAAC;AACD,aAAS,iBAAiB,SAAS,eAAe,UAAU;AAC5D,aAAS,iBAAiB,aAAa,eAAe,UAAU;AAChE,aAAS,iBAAiB,cAAc,kBAAkB,UAAU;AACpE,WAAO,WAAY;AACf,kBAAY,UAAU,OAAO,SAAU,MAAM;AAAE,eAAO,SAASA;AAAA,MAAO,CAAC;AACvE,eAAS,oBAAoB,SAAS,eAAe,UAAU;AAC/D,eAAS,oBAAoB,aAAa,eAAe,UAAU;AACnE,eAAS,oBAAoB,cAAc,kBAAkB,UAAU;AAAA,IAC3E;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,QAAc,sBAAcA,QAAO,EAAE,QAAQ,cAAc,EAAE,EAAE,CAAC,IAAI;AAAA,IACpE,kBAAwB,sBAAc,iBAAiB,EAAE,SAAS,MAAM,QAAQ,CAAC,IAAI;AAAA,EAAI;AACjG;AACA,SAAS,yBAAyB,MAAM;AACpC,MAAI,eAAe;AACnB,SAAO,SAAS,MAAM;AAClB,QAAI,gBAAgB,YAAY;AAC5B,qBAAe,KAAK;AACpB,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AACX;;;ASzJA,IAAO,kBAAQ,cAAc,WAAW,mBAAmB;;;ArBC3D,IAAI,oBAA0B,mBAAW,SAAU,OAAO,KAAK;AAAE,SAAc,sBAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,KAAU,SAAS,gBAAQ,CAAC,CAAC;AAAI,CAAC;AACnK,kBAAkB,aAAa,aAAa;AAC5C,IAAO,sBAAQ;;;AP8JL,IAAAC,sBAAA;AAvIV,IAAM,YAAY,CAAC,KAAK,SAAS,WAAW,WAAW;AACvD,IAAM,iBAAiB,CAAC,KAAK,OAAO;AAMpC,IAAM,cAAc;AAGpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAGzD,WAAW;AAGb,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,aAAa;EAC/E;EACA;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAoBzC,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAQ9F,IAAM,CAAC,6BAA6B,6BAA6B,IAC/D,oBAAqD,WAAW;AAkBlE,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM;IACJ;IACA;IACA,MAAM;IACN;IACA;IACA,OAAO;IACP;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM,cAAc,eAAe,aAAa;AAChD,QAAM,CAAC,SAAS,UAAU,IAAU,iBAAsC,IAAI;AAC9E,QAAM,CAAC,WAAW,YAAY,IAAU,iBAAoC,IAAI;AAChF,QAAM,CAAC,sBAAsB,uBAAuB,IAAU,iBAAS,KAAK;AAC5E,QAAM,YAAY,aAAa,GAAG;AAClC,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;IACnD,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AACD,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AACD,QAAM,2BAAiC,eAAwC,IAAI;AAGnF,QAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM,IAAI;AACpE,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,iBAAS,oBAAI,IAAkB,CAAC;AAOtF,QAAM,kBAAkB,MAAM,KAAK,gBAAgB,EAChD,IAAI,CAAC,WAAW,OAAO,MAAM,KAAK,EAClC,KAAK,GAAG;AAEX,aACE,yBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC;MACA,OAAO;MACP;MACA,iBAAiB;MACjB;MACA,mBAAmB;MACnB;MACA,8BAA8B;MAC9B,WAAW,MAAM;MACjB;MACA,eAAe;MACf;MACA,cAAc;MACd,KAAK;MACL;MACA;MAEA,UAAA;YAAA,yBAAC,WAAW,UAAX,EAAoB,OAAO,eAC1B,cAAA;UAAC;UAAA;YACC,OAAO,MAAM;YACb,mBAAyB,oBAAY,CAAC,WAAW;AAC/C,kCAAoB,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC;YACzD,GAAG,CAAC,CAAC;YACL,sBAA4B,oBAAY,CAAC,WAAW;AAClD,kCAAoB,CAAC,SAAS;AAC5B,sBAAM,aAAa,IAAI,IAAI,IAAI;AAC/B,2BAAW,OAAO,MAAM;AACxB,uBAAO;cACT,CAAC;YACH,GAAG,CAAC,CAAC;YAEJ;UAAA;QACH,EAAA,CACF;QAEC,oBACC;UAAC;UAAA;YAEC,eAAW;YACX;YACA,UAAU;YACV;YACA;YACA;YAEA,UAAU,CAAC,UAAU,SAAS,MAAM,OAAO,KAAK;YAChD;YACA;YAEC,UAAA;cAAA,UAAU,aAAY,yBAAC,UAAA,EAAO,OAAM,GAAA,CAAG,IAAK;cAC5C,MAAM,KAAK,gBAAgB;YAAA;UAAA;UAbvB;QAcP,IACE;MAAA;IAAA;EACN,EAAA,CACF;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,aAAa,IAAI;AAC7D,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,aAAa,QAAQ,YAAY;AACvC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,eAAe;AAC1E,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,iBAAuB,eAA0C,OAAO;AAE9E,UAAM,CAAC,WAAW,uBAAuB,cAAc,IAAI,mBAAmB,CAAC,WAAW;AACxF,YAAM,eAAe,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,UAAU,QAAQ,KAAK;AAC5E,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,aAAa,QAAW;AAC1B,gBAAQ,cAAc,SAAS,KAAK;MACtC;IACF,CAAC;AAED,UAAM,aAAa,CAAC,iBAAyD;AAC3E,UAAI,CAAC,YAAY;AACf,gBAAQ,aAAa,IAAI;AAEzB,uBAAe;MACjB;AAEA,UAAI,cAAc;AAChB,gBAAQ,yBAAyB,UAAU;UACzC,GAAG,KAAK,MAAM,aAAa,KAAK;UAChC,GAAG,KAAK,MAAM,aAAa,KAAK;QAClC;MACF;IACF;AAEA,eACE,yBAAiB,QAAhB,EAAuB,SAAO,MAAE,GAAG,aAClC,cAAA;MAAC,UAAU;MAAV;QACC,MAAK;QACL,MAAK;QACL,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,qBAAkB;QAClB,KAAK,QAAQ;QACb,cAAY,QAAQ,OAAO,SAAS;QACpC,UAAU;QACV,iBAAe,aAAa,KAAK;QACjC,oBAAkB,sBAAsB,QAAQ,KAAK,IAAI,KAAK;QAC7D,GAAG;QACJ,KAAK;QAEL,SAAS,qBAAqB,aAAa,SAAS,CAAC,UAAU;AAM7D,gBAAM,cAAc,MAAM;AAG1B,cAAI,eAAe,YAAY,SAAS;AACtC,uBAAW,KAAK;UAClB;QACF,CAAC;QACD,eAAe,qBAAqB,aAAa,eAAe,CAAC,UAAU;AACzE,yBAAe,UAAU,MAAM;AAI/B,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,mBAAO,sBAAsB,MAAM,SAAS;UAC9C;AAKA,cAAI,MAAM,WAAW,KAAK,MAAM,YAAY,SAAS,MAAM,gBAAgB,SAAS;AAClF,uBAAW,KAAK;AAEhB,kBAAM,eAAe;UACvB;QACF,CAAC;QACD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,gBAAM,gBAAgB,UAAU,YAAY;AAC5C,gBAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,cAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW,EAAG,uBAAsB,MAAM,GAAG;AAC7E,cAAI,iBAAiB,MAAM,QAAQ,IAAK;AACxC,cAAI,UAAU,SAAS,MAAM,GAAG,GAAG;AACjC,uBAAW;AACX,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAQnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AAEtD,UAAM,EAAE,eAAe,WAAW,OAAO,UAAU,cAAc,IAAI,GAAG,WAAW,IAAI;AACvF,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,EAAE,6BAA6B,IAAI;AACzC,UAAM,cAAc,aAAa;AACjC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,iBAAiB;AAE5E,qBAAgB,MAAM;AACpB,mCAA6B,WAAW;IAC1C,GAAG,CAAC,8BAA8B,WAAW,CAAC;AAE9C,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QAGL,OAAO,EAAE,eAAe,OAAO;QAE9B,UAAA,sBAAsB,QAAQ,KAAK,QAAI,yBAAA,8BAAA,EAAG,UAAA,YAAA,CAAY,IAAM;MAAA;IAC/D;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,YAAY;AAKlB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,eAAe,UAAU,GAAG,UAAU,IAAI;AAClD,eACE,yBAAC,UAAU,MAAV,EAAe,eAAW,MAAE,GAAG,WAAW,KAAK,cAC7C,UAAA,YAAY,IAAA,CACf;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,cAAc;AAWpB,IAAM,eAA4C,CAAC,UAA0C;AAC3F,aAAO,yBAAC,QAAA,EAAgB,SAAO,MAAE,GAAG,MAAA,CAAO;AAC7C;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAKrB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,CAAC,UAAU,WAAW,IAAU,iBAA2B;AAGjE,qBAAgB,MAAM;AACpB,kBAAY,IAAI,iBAAiB,CAAC;IACpC,GAAG,CAAC,CAAC;AAEL,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,OAAO;AACb,aAAO,OACM;YACP,yBAAC,uBAAA,EAAsB,OAAO,MAAM,eAClC,cAAA,yBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,eAC5B,cAAA,yBAAC,OAAA,EAAK,UAAA,MAAM,SAAA,CAAS,EAAA,CACvB,EAAA,CACF;QACA;MACF,IACA;IACN;AAEA,eAAO,yBAAC,mBAAA,EAAmB,GAAG,OAAO,KAAK,aAAA,CAAc;EAC1D;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,iBAAiB;AAqBvB,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,oBAA+C,YAAY;AAE7D,IAAM,oBAAoB;AA8B1B,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM;MACJ;MACA,WAAW;MACX;MACA;MACA;;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,CAAC,SAAS,UAAU,IAAU,iBAA0C,IAAI;AAClF,UAAM,CAAC,UAAU,WAAW,IAAU,iBAAuC,IAAI;AACjF,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,UAAM,CAAC,cAAc,eAAe,IAAU,iBAAmC,IAAI;AACrF,UAAM,CAAC,kBAAkB,mBAAmB,IAAU;MACpD;IACF;AACA,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,CAAC,cAAc,eAAe,IAAU,iBAAS,KAAK;AAC5D,UAAM,yBAA+B,eAAO,KAAK;AAG3C,IAAA,kBAAU,MAAM;AACpB,UAAI,QAAS,QAAO,WAAW,OAAO;IACxC,GAAG,CAAC,OAAO,CAAC;AAIZ,mBAAe;AAEf,UAAMC,cAAmB;MACvB,CAAC,eAA0C;AACzC,cAAM,CAAC,WAAW,GAAG,SAAS,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AAC3E,cAAM,CAAC,QAAQ,IAAI,UAAU,MAAM,EAAE;AAErC,cAAM,6BAA6B,SAAS;AAC5C,mBAAW,aAAa,YAAY;AAElC,cAAI,cAAc,2BAA4B;AAC9C,iDAAW,eAAe,EAAE,OAAO,UAAU;AAE7C,cAAI,cAAc,aAAa,SAAU,UAAS,YAAY;AAC9D,cAAI,cAAc,YAAY,SAAU,UAAS,YAAY,SAAS;AACtE,iDAAW;AACX,cAAI,SAAS,kBAAkB,2BAA4B;QAC7D;MACF;MACA,CAAC,UAAU,QAAQ;IACrB;AAEA,UAAM,oBAA0B;MAC9B,MAAMA,YAAW,CAAC,cAAc,OAAO,CAAC;MACxC,CAACA,aAAY,cAAc,OAAO;IACpC;AAIM,IAAA,kBAAU,MAAM;AACpB,UAAI,cAAc;AAChB,0BAAkB;MACpB;IACF,GAAG,CAAC,cAAc,iBAAiB,CAAC;AAIpC,UAAM,EAAE,cAAc,yBAAyB,IAAI;AAC7C,IAAA,kBAAU,MAAM;AACpB,UAAI,SAAS;AACX,YAAI,mBAAmB,EAAE,GAAG,GAAG,GAAG,EAAE;AAEpC,cAAM,oBAAoB,CAAC,UAAwB;;AACjD,6BAAmB;YACjB,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;YAChF,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;UAClF;QACF;AACA,cAAM,kBAAkB,CAAC,UAAwB;AAE/C,cAAI,iBAAiB,KAAK,MAAM,iBAAiB,KAAK,IAAI;AACxD,kBAAM,eAAe;UACvB,OAAO;AAEL,gBAAI,CAAC,QAAQ,SAAS,MAAM,MAAqB,GAAG;AAClD,2BAAa,KAAK;YACpB;UACF;AACA,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mCAAyB,UAAU;QACrC;AAEA,YAAI,yBAAyB,YAAY,MAAM;AAC7C,mBAAS,iBAAiB,eAAe,iBAAiB;AAC1D,mBAAS,iBAAiB,aAAa,iBAAiB,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;QACvF;AAEA,eAAO,MAAM;AACX,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mBAAS,oBAAoB,aAAa,iBAAiB,EAAE,SAAS,KAAK,CAAC;QAC9E;MACF;IACF,GAAG,CAAC,SAAS,cAAc,wBAAwB,CAAC;AAE9C,IAAA,kBAAU,MAAM;AACpB,YAAM,QAAQ,MAAM,aAAa,KAAK;AACtC,aAAO,iBAAiB,QAAQ,KAAK;AACrC,aAAO,iBAAiB,UAAU,KAAK;AACvC,aAAO,MAAM;AACX,eAAO,oBAAoB,QAAQ,KAAK;AACxC,eAAO,oBAAoB,UAAU,KAAK;MAC5C;IACF,GAAG,CAAC,YAAY,CAAC;AAEjB,UAAM,CAAC,WAAW,qBAAqB,IAAI,mBAAmB,CAAC,WAAW;AACxE,YAAM,eAAe,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AAC3F,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,UAAU;AAKZ,mBAAW,MAAO,SAAS,IAAI,QAAwB,MAAM,CAAC;MAChE;IACF,CAAC;AAED,UAAM,kBAAwB;MAC5B,CAAC,MAAgC,OAAe,aAAsB;AACpE,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAa,QAAQ,UAAU;AACxE,YAAI,kBAAkB,kBAAkB;AACtC,0BAAgB,IAAI;AACpB,cAAI,iBAAkB,wBAAuB,UAAU;QACzD;MACF;MACA,CAAC,QAAQ,KAAK;IAChB;AACA,UAAM,kBAAwB,oBAAY,MAAM,mCAAS,SAAS,CAAC,OAAO,CAAC;AAC3E,UAAM,sBAA4B;MAChC,CAAC,MAAoC,OAAe,aAAsB;AACxE,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAa,QAAQ,UAAU;AACxE,YAAI,kBAAkB,kBAAkB;AACtC,8BAAoB,IAAI;QAC1B;MACF;MACA,CAAC,QAAQ,KAAK;IAChB;AAEA,UAAM,iBAAiB,aAAa,WAAW,uBAAuB;AAGtE,UAAM,qBACJ,mBAAmB,uBACf;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IACA,CAAC;AAEP,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA,kBAAkB;QAClB;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,cAAA,yBAAC,qBAAA,EAAa,IAAI,MAAM,gBAAc,MACpC,cAAA;UAAC;UAAA;YACC,SAAO;YAGP,SAAS,QAAQ;YACjB,kBAAkB,CAAC,UAAU;AAE3B,oBAAM,eAAe;YACvB;YACA,oBAAoB,qBAAqB,kBAAkB,CAAC,UAAU;;AACpE,4BAAQ,YAAR,mBAAiB,MAAM,EAAE,eAAe,KAAK;AAC7C,oBAAM,eAAe;YACvB,CAAC;YAED,cAAA;cAAC;cAAA;gBACC,SAAO;gBACP,6BAA2B;gBAC3B;gBACA;gBAGA,gBAAgB,CAAC,UAAU,MAAM,eAAe;gBAChD,WAAW,MAAM,QAAQ,aAAa,KAAK;gBAE3C,cAAA;kBAAC;kBAAA;oBACC,MAAK;oBACL,IAAI,QAAQ;oBACZ,cAAY,QAAQ,OAAO,SAAS;oBACpC,KAAK,QAAQ;oBACb,eAAe,CAAC,UAAU,MAAM,eAAe;oBAC9C,GAAG;oBACH,GAAG;oBACJ,UAAU,MAAM,gBAAgB,IAAI;oBACpC,KAAK;oBACL,OAAO;;sBAEL,SAAS;sBACT,eAAe;;sBAEf,SAAS;sBACT,GAAG,aAAa;oBAClB;oBACA,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,4BAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAG7D,0BAAI,MAAM,QAAQ,MAAO,OAAM,eAAe;AAE9C,0BAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW,EAAG,uBAAsB,MAAM,GAAG;AAE7E,0BAAI,CAAC,WAAW,aAAa,QAAQ,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC/D,8BAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,4BAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAE1D,4BAAI,CAAC,WAAW,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC1C,2CAAiB,eAAe,MAAM,EAAE,QAAQ;wBAClD;AACA,4BAAI,CAAC,WAAW,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AAChD,gCAAM,iBAAiB,MAAM;AAC7B,gCAAM,eAAe,eAAe,QAAQ,cAAc;AAC1D,2CAAiB,eAAe,MAAM,eAAe,CAAC;wBACxD;AAMA,mCAAW,MAAMA,YAAW,cAAc,CAAC;AAE3C,8BAAM,eAAe;sBACvB;oBACF,CAAC;kBAAA;gBACH;cAAA;YACF;UAAA;QACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,6BAA6B;AAKnC,IAAM,4BAAkC,mBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM,EAAE,eAAe,UAAU,GAAG,YAAY,IAAI;AACpD,QAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,QAAM,iBAAiB,wBAAwB,cAAc,aAAa;AAC1E,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,iBAAgC,IAAI;AACtF,QAAM,CAAC,SAAS,UAAU,IAAU,iBAAkD,IAAI;AAC1F,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,QAAM,WAAW,cAAc,aAAa;AAC5C,QAAM,0BAAgC,eAAO,KAAK;AAClD,QAAM,sBAA4B,eAAO,IAAI;AAE7C,QAAM,EAAE,UAAU,cAAc,kBAAkB,kBAAkB,IAAI;AACxE,QAAM,WAAiB,oBAAY,MAAM;AACvC,QACE,QAAQ,WACR,QAAQ,aACR,kBACA,WACA,YACA,gBACA,kBACA;AACA,YAAM,cAAc,QAAQ,QAAQ,sBAAsB;AAK1D,YAAM,cAAc,QAAQ,sBAAsB;AAClD,YAAM,gBAAgB,QAAQ,UAAU,sBAAsB;AAC9D,YAAM,eAAe,iBAAiB,sBAAsB;AAE5D,UAAI,QAAQ,QAAQ,OAAO;AACzB,cAAM,iBAAiB,aAAa,OAAO,YAAY;AACvD,cAAM,OAAO,cAAc,OAAO;AAClC,cAAM,YAAY,YAAY,OAAO;AACrC,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,YAAY,OAAO,aAAa;AACtC,cAAM,cAAc,MAAM,MAAM;UAC9B;;;;;;UAMA,KAAK,IAAI,gBAAgB,YAAY,YAAY;QACnD,CAAC;AAED,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,OAAO,cAAc;MAC5C,OAAO;AACL,cAAM,iBAAiB,YAAY,QAAQ,aAAa;AACxD,cAAM,QAAQ,OAAO,aAAa,cAAc,QAAQ;AACxD,cAAM,aAAa,OAAO,aAAa,YAAY,QAAQ;AAC3D,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,WAAW,OAAO,aAAa;AACrC,cAAM,eAAe,MAAM,OAAO;UAChC;UACA,KAAK,IAAI,gBAAgB,WAAW,YAAY;QAClD,CAAC;AAED,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,QAAQ,eAAe;MAC9C;AAKA,YAAM,QAAQ,SAAS;AACvB,YAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,YAAM,cAAc,SAAS;AAE7B,YAAM,gBAAgB,OAAO,iBAAiB,OAAO;AACrD,YAAM,wBAAwB,SAAS,cAAc,gBAAgB,EAAE;AACvE,YAAM,oBAAoB,SAAS,cAAc,YAAY,EAAE;AAC/D,YAAM,2BAA2B,SAAS,cAAc,mBAAmB,EAAE;AAC7E,YAAM,uBAAuB,SAAS,cAAc,eAAe,EAAE;AACrE,YAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;AAC3G,YAAM,mBAAmB,KAAK,IAAI,aAAa,eAAe,GAAG,iBAAiB;AAElF,YAAM,iBAAiB,OAAO,iBAAiB,QAAQ;AACvD,YAAM,qBAAqB,SAAS,eAAe,YAAY,EAAE;AACjE,YAAM,wBAAwB,SAAS,eAAe,eAAe,EAAE;AAEvE,YAAM,yBAAyB,YAAY,MAAM,YAAY,SAAS,IAAI;AAC1E,YAAM,4BAA4B,kBAAkB;AAEpD,YAAM,yBAAyB,aAAa,eAAe;AAC3D,YAAM,mBAAmB,aAAa,YAAY;AAClD,YAAM,yBAAyB,wBAAwB,oBAAoB;AAC3E,YAAM,4BAA4B,oBAAoB;AAEtD,YAAM,8BAA8B,0BAA0B;AAE9D,UAAI,6BAA6B;AAC/B,cAAM,aAAa,MAAM,SAAS,KAAK,iBAAiB,MAAM,MAAM,SAAS,CAAC,EAAE,IAAI;AACpF,uBAAe,MAAM,SAAS;AAC9B,cAAM,uBACJ,QAAQ,eAAe,SAAS,YAAY,SAAS;AACvD,cAAM,mCAAmC,KAAK;UAC5C;UACA;WAEG,aAAa,wBAAwB,KACtC,uBACA;QACJ;AACA,cAAM,SAAS,yBAAyB;AACxC,uBAAe,MAAM,SAAS,SAAS;MACzC,OAAO;AACL,cAAM,cAAc,MAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC,EAAE,IAAI;AACtE,uBAAe,MAAM,MAAM;AAC3B,cAAM,gCAAgC,KAAK;UACzC;UACA,wBACE,SAAS;WAER,cAAc,qBAAqB,KACpC;QACJ;AACA,cAAM,SAAS,gCAAgC;AAC/C,uBAAe,MAAM,SAAS,SAAS;AACvC,iBAAS,YAAY,yBAAyB,yBAAyB,SAAS;MAClF;AAEA,qBAAe,MAAM,SAAS,GAAG,cAAc;AAC/C,qBAAe,MAAM,YAAY,mBAAmB;AACpD,qBAAe,MAAM,YAAY,kBAAkB;AAGnD;AAIA,4BAAsB,MAAO,wBAAwB,UAAU,IAAK;IACtE;EACF,GAAG;IACD;IACA,QAAQ;IACR,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;EACF,CAAC;AAED,mBAAgB,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC;AAG5C,QAAM,CAAC,eAAe,gBAAgB,IAAU,iBAAiB;AACjE,mBAAgB,MAAM;AACpB,QAAI,QAAS,kBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;EACvE,GAAG,CAAC,OAAO,CAAC;AAMZ,QAAM,2BAAiC;IACrC,CAAC,SAA+C;AAC9C,UAAI,QAAQ,oBAAoB,YAAY,MAAM;AAChD,iBAAS;AACT;AACA,4BAAoB,UAAU;MAChC;IACF;IACA,CAAC,UAAU,iBAAiB;EAC9B;AAEA,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA,sBAAsB;MAEtB,cAAA;QAAC;QAAA;UACC,KAAK;UACL,OAAO;YACL,SAAS;YACT,eAAe;YACf,UAAU;YACV,QAAQ;UACV;UAEA,cAAA;YAAC,UAAU;YAAV;cACE,GAAG;cACJ,KAAK;cACL,OAAO;;;gBAGL,WAAW;;gBAEX,WAAW;gBACX,GAAG,YAAY;cACjB;YAAA;UACF;QAAA;MACF;IAAA;EACF;AAEJ,CAAC;AAED,0BAA0B,cAAc;AAMxC,IAAM,uBAAuB;AAM7B,IAAM,uBAA6B,mBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;IACJ;IACA,QAAQ;IACR,mBAAmB;IACnB,GAAG;EACL,IAAI;AACJ,QAAM,cAAc,eAAe,aAAa;AAEhD,aACE;IAAiB;IAAhB;MACE,GAAG;MACH,GAAG;MACJ,KAAK;MACL;MACA;MACA,OAAO;;QAEL,WAAW;QACX,GAAG,YAAY;;QAEf,GAAG;UACD,2CAA2C;UAC3C,0CAA0C;UAC1C,2CAA2C;UAC3C,gCAAgC;UAChC,iCAAiC;QACnC;MACF;IAAA;EACF;AAEJ,CAAC;AAED,qBAAqB,cAAc;AAYnC,IAAM,CAAC,wBAAwB,wBAAwB,IACrD,oBAAgD,cAAc,CAAC,CAAC;AAElE,IAAM,gBAAgB;AAQtB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,eAAe,OAAO,GAAG,cAAc,IAAI;AACnD,UAAM,iBAAiB,wBAAwB,eAAe,aAAa;AAC3E,UAAM,kBAAkB,yBAAyB,eAAe,aAAa;AAC7E,UAAM,eAAe,gBAAgB,cAAc,eAAe,gBAAgB;AAClF,UAAM,mBAAyB,eAAO,CAAC;AACvC,eACE,0BAAA,8BAAA,EAEE,UAAA;UAAA;QAAC;QAAA;UACC,yBAAyB;YACvB,QAAQ;UACV;UACA;QAAA;MACF;UACA,yBAAC,WAAW,MAAX,EAAgB,OAAO,eACtB,cAAA;QAAC,UAAU;QAAV;UACC,8BAA2B;UAC3B,MAAK;UACJ,GAAG;UACJ,KAAK;UACL,OAAO;;;;YAIL,UAAU;YACV,MAAM;;;;;YAKN,UAAU;YACV,GAAG,cAAc;UACnB;UACA,UAAU,qBAAqB,cAAc,UAAU,CAAC,UAAU;AAChE,kBAAM,WAAW,MAAM;AACvB,kBAAM,EAAE,gBAAgB,wBAAwB,IAAI;AACpD,iBAAI,mEAAyB,YAAW,gBAAgB;AACtD,oBAAM,aAAa,KAAK,IAAI,iBAAiB,UAAU,SAAS,SAAS;AACzE,kBAAI,aAAa,GAAG;AAClB,sBAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,sBAAM,eAAe,WAAW,eAAe,MAAM,SAAS;AAC9D,sBAAM,YAAY,WAAW,eAAe,MAAM,MAAM;AACxD,sBAAM,aAAa,KAAK,IAAI,cAAc,SAAS;AAEnD,oBAAI,aAAa,iBAAiB;AAChC,wBAAM,aAAa,aAAa;AAChC,wBAAM,oBAAoB,KAAK,IAAI,iBAAiB,UAAU;AAC9D,wBAAM,aAAa,aAAa;AAEhC,iCAAe,MAAM,SAAS,oBAAoB;AAClD,sBAAI,eAAe,MAAM,WAAW,OAAO;AACzC,6BAAS,YAAY,aAAa,IAAI,aAAa;AAEnD,mCAAe,MAAM,iBAAiB;kBACxC;gBACF;cACF;YACF;AACA,6BAAiB,UAAU,SAAS;UACtC,CAAC;QAAA;MACH,EAAA,CACF;IAAA,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,aAAa;AAInB,IAAM,CAAC,4BAA4B,qBAAqB,IACtD,oBAA6C,UAAU;AAKzD,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,MAAM;AACtB,eACE,yBAAC,4BAAA,EAA2B,OAAO,eAAe,IAAI,SACpD,cAAA,yBAAC,UAAU,KAAV,EAAc,MAAK,SAAQ,mBAAiB,SAAU,GAAG,YAAY,KAAK,aAAA,CAAc,EAAA,CAC3F;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,eAAe,sBAAsB,YAAY,aAAa;AACpE,eAAO,yBAAC,UAAU,KAAV,EAAc,IAAI,aAAa,IAAK,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,YAAY;AAUlB,IAAM,CAAC,2BAA2B,oBAAoB,IACpD,oBAA4C,SAAS;AASvD,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;MACJ;MACA;MACA,WAAW;MACX,WAAW;MACX,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,WAAW,aAAa;AACzD,UAAM,iBAAiB,wBAAwB,WAAW,aAAa;AACvE,UAAM,aAAa,QAAQ,UAAU;AACrC,UAAM,CAAC,WAAW,YAAY,IAAU,iBAAS,iBAAiB,EAAE;AACpE,UAAM,CAAC,WAAW,YAAY,IAAU,iBAAS,KAAK;AACtD,UAAM,eAAe;MAAgB;MAAc,CAAC,SAAA;;AAClD,oCAAe,oBAAf,wCAAiC,MAAM,OAAO;;IAChD;AACA,UAAM,SAAS,MAAM;AACrB,UAAM,iBAAuB,eAA0C,OAAO;AAE9E,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,UAAU;AACb,gBAAQ,cAAc,KAAK;AAC3B,gBAAQ,aAAa,KAAK;MAC5B;IACF;AAEA,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI;QACR;MACF;IACF;AAEA,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,kBAAwB,oBAAY,CAAC,SAAS;AAC5C,uBAAa,CAAC,kBAAkB,mBAAkB,6BAAM,gBAAe,IAAI,KAAK,CAAC;QACnF,GAAG,CAAC,CAAC;QAEL,cAAA;UAAC,WAAW;UAAX;YACC,OAAO;YACP;YACA;YACA;YAEA,cAAA;cAAC,UAAU;cAAV;gBACC,MAAK;gBACL,mBAAiB;gBACjB,oBAAkB,YAAY,KAAK;gBAEnC,iBAAe,cAAc;gBAC7B,cAAY,aAAa,YAAY;gBACrC,iBAAe,YAAY;gBAC3B,iBAAe,WAAW,KAAK;gBAC/B,UAAU,WAAW,SAAY;gBAChC,GAAG;gBACJ,KAAK;gBACL,SAAS,qBAAqB,UAAU,SAAS,MAAM,aAAa,IAAI,CAAC;gBACzE,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,aAAa,KAAK,CAAC;gBACxE,SAAS,qBAAqB,UAAU,SAAS,MAAM;AAErD,sBAAI,eAAe,YAAY,QAAS,cAAa;gBACvD,CAAC;gBACD,aAAa,qBAAqB,UAAU,aAAa,MAAM;AAG7D,sBAAI,eAAe,YAAY,QAAS,cAAa;gBACvD,CAAC;gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;AACtE,iCAAe,UAAU,MAAM;gBACjC,CAAC;gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;;AAEtE,iCAAe,UAAU,MAAM;AAC/B,sBAAI,UAAU;AACZ,yCAAe,gBAAf;kBACF,WAAW,eAAe,YAAY,SAAS;AAG7C,0BAAM,cAAc,MAAM,EAAE,eAAe,KAAK,CAAC;kBACnD;gBACF,CAAC;gBACD,gBAAgB,qBAAqB,UAAU,gBAAgB,CAAC,UAAU;;AACxE,sBAAI,MAAM,kBAAkB,SAAS,eAAe;AAClD,yCAAe,gBAAf;kBACF;gBACF,CAAC;gBACD,WAAW,qBAAqB,UAAU,WAAW,CAAC,UAAU;;AAC9D,wBAAM,kBAAgB,oBAAe,cAAf,mBAA0B,aAAY;AAC5D,sBAAI,iBAAiB,MAAM,QAAQ,IAAK;AACxC,sBAAI,eAAe,SAAS,MAAM,GAAG,EAAG,cAAa;AAErD,sBAAI,MAAM,QAAQ,IAAK,OAAM,eAAe;gBAC9C,CAAC;cAAA;YACH;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,iBAAiB;AAKvB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AAEzD,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,cAAc,IAAI;AAC9D,UAAM,UAAU,iBAAiB,gBAAgB,aAAa;AAC9D,UAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;AAC5E,UAAM,cAAc,qBAAqB,gBAAgB,aAAa;AACtE,UAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;AACxF,UAAM,CAAC,cAAc,eAAe,IAAU,iBAAuC,IAAI;AACzF,UAAM,eAAe;MACnB;MACA,CAAC,SAAS,gBAAgB,IAAI;MAC9B,YAAY;MACZ,CAAC,SAAA;;AAAS,oCAAe,wBAAf,wCAAqC,MAAM,YAAY,OAAO,YAAY;;IACtF;AAEA,UAAM,cAAc,6CAAc;AAClC,UAAM,eAAqB;MACzB,UACE,yBAAC,UAAA,EAA+B,OAAO,YAAY,OAAO,UAAU,YAAY,UAC7E,UAAA,YAAA,GADU,YAAY,KAEzB;MAEF,CAAC,YAAY,UAAU,YAAY,OAAO,WAAW;IACvD;AAEA,UAAM,EAAE,mBAAmB,qBAAqB,IAAI;AACpD,qBAAgB,MAAM;AACpB,wBAAkB,YAAY;AAC9B,aAAO,MAAM,qBAAqB,YAAY;IAChD,GAAG,CAAC,mBAAmB,sBAAsB,YAAY,CAAC;AAE1D,eACE,0BAAA,8BAAA,EACE,UAAA;UAAA,yBAAC,UAAU,MAAV,EAAe,IAAI,YAAY,QAAS,GAAG,eAAe,KAAK,aAAA,CAAc;MAG7E,YAAY,cAAc,QAAQ,aAAa,CAAC,QAAQ,uBAC5C,sBAAa,cAAc,UAAU,QAAQ,SAAS,IAC/D;IAAA,EAAA,CACN;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,sBAAsB;AAK5B,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,eAAe,GAAG,mBAAmB,IAAI;AACjD,UAAM,cAAc,qBAAqB,qBAAqB,aAAa;AAC3E,WAAO,YAAY,iBACjB,yBAAC,UAAU,MAAV,EAAe,eAAW,MAAE,GAAG,oBAAoB,KAAK,aAAA,CAAc,IACrE;EACN;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAM,wBAAwB;AAK9B,IAAM,uBAA6B,mBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;AACzF,QAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;AAC3F,QAAM,CAAC,aAAa,cAAc,IAAU,iBAAS,KAAK;AAC1D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AAEvF,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAE1D,UAASC,iBAAT,WAAwB;AACtB,cAAMC,eAAc,SAAS,YAAY;AACzC,uBAAeA,YAAW;MAC5B;AAHS,UAAAD,gBAAAA;AADT,YAAM,WAAW,eAAe;AAKhCA,MAAAA,eAAa;AACb,eAAS,iBAAiB,UAAUA,cAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAUA,cAAY;IAClE;EACF,GAAG,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AAEzD,SAAO,kBACL;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAa,IAAI;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;QACzD;MACF;IAAA;EACF,IACE;AACN,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,0BAA0B;AAKhC,IAAM,yBAA+B,mBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;AAC3F,QAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;AAC7F,QAAM,CAAC,eAAe,gBAAgB,IAAU,iBAAS,KAAK;AAC9D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AAEvF,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAE1D,UAASA,iBAAT,WAAwB;AACtB,cAAM,YAAY,SAAS,eAAe,SAAS;AAGnD,cAAME,iBAAgB,KAAK,KAAK,SAAS,SAAS,IAAI;AACtD,yBAAiBA,cAAa;MAChC;AANS,UAAAF,gBAAAA;AADT,YAAM,WAAW,eAAe;AAQhCA,MAAAA,eAAa;AACb,eAAS,iBAAiB,UAAUA,cAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAUA,cAAY;IAClE;EACF,GAAG,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AAEzD,SAAO,oBACL;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAa,IAAI;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;QACzD;MACF;IAAA;EACF,IACE;AACN,CAAC;AAED,uBAAuB,cAAc;AAOrC,IAAM,yBAA+B,mBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,eAAe,cAAc,GAAG,qBAAqB,IAAI;AACjE,QAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;AAClF,QAAM,qBAA2B,eAAsB,IAAI;AAC3D,QAAM,WAAW,cAAc,aAAa;AAE5C,QAAM,uBAA6B,oBAAY,MAAM;AACnD,QAAI,mBAAmB,YAAY,MAAM;AACvC,aAAO,cAAc,mBAAmB,OAAO;AAC/C,yBAAmB,UAAU;IAC/B;EACF,GAAG,CAAC,CAAC;AAEC,EAAA,kBAAU,MAAM;AACpB,WAAO,MAAM,qBAAqB;EACpC,GAAG,CAAC,oBAAoB,CAAC;AAMzB,mBAAgB,MAAM;;AACpB,UAAM,aAAa,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AACxF,mDAAY,IAAI,YAAhB,mBAAyB,eAAe,EAAE,OAAO,UAAU;EAC7D,GAAG,CAAC,QAAQ,CAAC;AAEb,aACE;IAAC,UAAU;IAAV;MACC,eAAW;MACV,GAAG;MACJ,KAAK;MACL,OAAO,EAAE,YAAY,GAAG,GAAG,qBAAqB,MAAM;MACtD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;AAC5E,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;QAClE;MACF,CAAC;MACD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;;AAC5E,6BAAe,gBAAf;AACA,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;QAClE;MACF,CAAC;MACD,gBAAgB,qBAAqB,qBAAqB,gBAAgB,MAAM;AAC9E,6BAAqB;MACvB,CAAC;IAAA;EACH;AAEJ,CAAC;AAMD,IAAM,iBAAiB;AAKvB,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,eAAe,GAAG,eAAe,IAAI;AAC7C,eAAO,yBAAC,UAAU,KAAV,EAAc,eAAW,MAAE,GAAG,gBAAgB,KAAK,aAAA,CAAc;EAC3E;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAM,aAAa;AAMnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,iBAAiB,wBAAwB,YAAY,aAAa;AACxE,WAAO,QAAQ,QAAQ,eAAe,aAAa,eACjD,yBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc,IACzE;EACN;AACF;AAEA,YAAY,cAAc;AAI1B,SAAS,sBAAsB,OAAgB;AAC7C,SAAO,UAAU,MAAM,UAAU;AACnC;AAEA,IAAM,eAAqB;EACzB,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,OAAO,GAAG,YAAY,IAAI;AAClC,UAAM,MAAY,eAA0B,IAAI;AAChD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,YAAY,YAAY,KAAK;AAG7B,IAAA,kBAAU,MAAM;AACpB,YAAM,SAAS,IAAI;AACnB,YAAM,cAAc,OAAO,kBAAkB;AAC7C,YAAM,aAAa,OAAO;QACxB;QACA;MACF;AACA,YAAM,WAAW,WAAW;AAC5B,UAAI,cAAc,SAAS,UAAU;AACnC,cAAM,QAAQ,IAAI,MAAM,UAAU,EAAE,SAAS,KAAK,CAAC;AACnD,iBAAS,KAAK,QAAQ,KAAK;AAC3B,eAAO,cAAc,KAAK;MAC5B;IACF,GAAG,CAAC,WAAW,KAAK,CAAC;AAcrB,eACE,yBAAC,gBAAA,EAAe,SAAO,MACrB,cAAA,yBAAC,UAAA,EAAQ,GAAG,aAAa,KAAK,cAAc,cAAc,MAAA,CAAO,EAAA,CACnE;EAEJ;AACF;AAEA,aAAa,cAAc;AAE3B,SAAS,mBAAmB,gBAA0C;AACpE,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,YAAkB,eAAO,EAAE;AACjC,QAAM,WAAiB,eAAO,CAAC;AAE/B,QAAM,wBAA8B;IAClC,CAAC,QAAgB;AACf,YAAM,SAAS,UAAU,UAAU;AACnC,yBAAmB,MAAM;AAEzB,OAAC,SAAS,aAAa,OAAe;AACpC,kBAAU,UAAU;AACpB,eAAO,aAAa,SAAS,OAAO;AAEpC,YAAI,UAAU,GAAI,UAAS,UAAU,OAAO,WAAW,MAAM,aAAa,EAAE,GAAG,GAAI;MACrF,GAAG,MAAM;IACX;IACA,CAAC,kBAAkB;EACrB;AAEA,QAAM,iBAAuB,oBAAY,MAAM;AAC7C,cAAU,UAAU;AACpB,WAAO,aAAa,SAAS,OAAO;EACtC,GAAG,CAAC,CAAC;AAEC,EAAA,kBAAU,MAAM;AACpB,WAAO,MAAM,OAAO,aAAa,SAAS,OAAO;EACnD,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,WAAW,uBAAuB,cAAc;AAC1D;AAmBA,SAAS,aACP,OACA,QACA,aACA;AACA,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAC7F,QAAM,mBAAmB,aAAa,OAAO,CAAC,IAAI;AAClD,QAAM,mBAAmB,cAAc,MAAM,QAAQ,WAAW,IAAI;AACpE,MAAI,eAAe,UAAU,OAAO,KAAK,IAAI,kBAAkB,CAAC,CAAC;AACjE,QAAM,qBAAqB,iBAAiB,WAAW;AACvD,MAAI,mBAAoB,gBAAe,aAAa,OAAO,CAAC,MAAM,MAAM,WAAW;AACnF,QAAM,WAAW,aAAa;IAAK,CAAC,SAClC,KAAK,UAAU,YAAY,EAAE,WAAW,iBAAiB,YAAY,CAAC;EACxE;AACA,SAAO,aAAa,cAAc,WAAW;AAC/C;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAC;AAC3E;AAEA,IAAMG,SAAO;AACb,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAMC,SAAQ;", "names": ["React", "React", "handleFocusIn", "handleFocusOut", "handleMutations", "container", "React", "__assign", "React", "React", "useCallbackRef", "React", "useCallbackRef", "React", "import_react", "cbs", "React", "import_react", "React", "SideCar", "React", "React", "React", "Style", "import_jsx_runtime", "focusFirst", "handleScroll", "canScrollUp", "canScrollDown", "Root", "Portal", "Content", "Arrow"]}