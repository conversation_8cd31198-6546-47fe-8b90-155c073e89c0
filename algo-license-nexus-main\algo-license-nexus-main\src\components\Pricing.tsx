
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Crown, Star, Zap } from "lucide-react";
import { cn } from "@/lib/utils";

export const Pricing = () => {
  const plans = [
    {
      name: "Select",
      price: "$50,000",
      period: "per strategy",
      description: "Perfect for emerging funds and boutique firms",
      icon: Star,
      color: "from-blue-500 to-cyan-500",
      features: [
        "Access to Tier 2 strategies",
        "Basic implementation support",
        "Quarterly performance reports",
        "Email support",
        "1-year license term"
      ],
      popular: false
    },
    {
      name: "Elite",
      price: "$150,000",
      period: "per strategy",
      description: "For established institutions seeking premium algorithms",
      icon: Crown,
      color: "from-purple-500 to-pink-500",
      features: [
        "Access to Tier 1 & 2 strategies",
        "Dedicated implementation team",
        "Monthly performance analysis",
        "Priority phone & email support",
        "Custom parameter tuning",
        "2-year license term"
      ],
      popular: true
    },
    {
      name: "Sovereign",
      price: "Custom",
      period: "enterprise pricing",
      description: "Exclusive partnerships for market leaders",
      icon: Zap,
      color: "from-gold-400 to-yellow-500",
      features: [
        "Exclusive strategy development",
        "Full white-label licensing",
        "Dedicated research team",
        "24/7 premium support",
        "Real-time performance monitoring",
        "Unlimited license term",
        "IP co-development rights"
      ],
      popular: false
    }
  ];

  return (
    <section className="py-24 px-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Investment <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Tiers</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose the licensing tier that matches your firm's ambitions and scale.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={cn(
                "group glass-subtle border-white/8 hover:border-white/15 transition-all duration-500 hover-lift hover-glow relative animate-scale-in",
                plan.popular ? 'ring-1 ring-purple-500/30 shadow-glow-subtle' : ''
              )}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-purple-500 via-pink-500 to-purple-500 text-white px-5 py-2 rounded-full font-semibold text-xs shadow-glow-subtle animate-pulse-subtle">
                    Most Popular
                  </div>
                </div>
              )}

              <CardHeader className="text-center pb-8 relative">
                <div className={`w-18 h-18 mx-auto mb-6 rounded-3xl bg-gradient-to-br ${plan.color} flex items-center justify-center shadow-glow-subtle group-hover:scale-105 transition-transform duration-300 relative`}>
                  <plan.icon className="w-9 h-9 text-white group-hover:animate-pulse-subtle" />
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <CardTitle className="text-3xl font-black text-white mb-3 group-hover:text-gradient-blue transition-all duration-300">
                  {plan.name}
                </CardTitle>
                <CardDescription className="text-gray-400 mb-6 group-hover:text-gray-300 transition-colors duration-300">
                  {plan.description}
                </CardDescription>
                <div className="text-center">
                  <span className="text-5xl font-black text-gradient-blue">{plan.price}</span>
                  <span className="text-gray-400 text-lg ml-2 block mt-1">{plan.period}</span>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {plan.features.map((feature, idx) => (
                    <div
                      key={idx}
                      className="flex items-start text-gray-300 group-hover:text-gray-200 transition-colors duration-300 animate-slide-up"
                      style={{ animationDelay: `${(index * 0.2) + (idx * 0.1)}s` }}
                    >
                      <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0 mt-0.5 group-hover:text-green-300 transition-colors duration-300" />
                      <span className="leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="pt-6">
                  <Button className={cn(
                    "w-full bg-gradient-to-r font-bold py-4 text-lg rounded-2xl transition-all duration-500 hover:scale-105 shadow-glow group",
                    plan.color.includes('gold')
                      ? `bg-gradient-to-r ${plan.color} text-black hover:shadow-glow`
                      : `bg-gradient-to-r ${plan.color} text-white hover:shadow-glow-purple`
                  )}>
                    <span className="relative z-10">
                      {plan.price === "Custom" ? "Contact Sales" : "License Strategy"}
                    </span>
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <p className="text-gray-400 text-lg mb-4">
            All licenses include comprehensive due diligence documentation and compliance support.
          </p>
          <p className="text-sm text-gray-500">
            Pricing subject to strategy complexity and exclusivity terms. Contact our team for detailed proposals.
          </p>
        </div>
      </div>
    </section>
  );
};
