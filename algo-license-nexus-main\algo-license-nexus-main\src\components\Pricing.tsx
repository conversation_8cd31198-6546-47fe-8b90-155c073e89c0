
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Crown, Star, Zap } from "lucide-react";

export const Pricing = () => {
  const plans = [
    {
      name: "Select",
      price: "$50,000",
      period: "per strategy",
      description: "Perfect for emerging funds and boutique firms",
      icon: Star,
      color: "from-blue-500 to-cyan-500",
      features: [
        "Access to Tier 2 strategies",
        "Basic implementation support",
        "Quarterly performance reports",
        "Email support",
        "1-year license term"
      ],
      popular: false
    },
    {
      name: "Elite",
      price: "$150,000",
      period: "per strategy",
      description: "For established institutions seeking premium algorithms",
      icon: Crown,
      color: "from-purple-500 to-pink-500",
      features: [
        "Access to Tier 1 & 2 strategies",
        "Dedicated implementation team",
        "Monthly performance analysis",
        "Priority phone & email support",
        "Custom parameter tuning",
        "2-year license term"
      ],
      popular: true
    },
    {
      name: "Sovereign",
      price: "Custom",
      period: "enterprise pricing",
      description: "Exclusive partnerships for market leaders",
      icon: Zap,
      color: "from-gold-400 to-yellow-500",
      features: [
        "Exclusive strategy development",
        "Full white-label licensing",
        "Dedicated research team",
        "24/7 premium support",
        "Real-time performance monitoring",
        "Unlimited license term",
        "IP co-development rights"
      ],
      popular: false
    }
  ];

  return (
    <section className="py-24 px-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Investment <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Tiers</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose the licensing tier that matches your firm's ambitions and scale.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <Card key={index} className={`bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 relative ${plan.popular ? 'ring-2 ring-purple-500 ring-opacity-50' : ''}`}>
              {plan.popular && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-8">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${plan.color} flex items-center justify-center`}>
                  <plan.icon className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl text-white mb-2">{plan.name}</CardTitle>
                <CardDescription className="text-gray-400 mb-4">{plan.description}</CardDescription>
                <div className="text-center">
                  <span className="text-4xl font-bold text-white">{plan.price}</span>
                  <span className="text-gray-400 text-lg ml-2">{plan.period}</span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {plan.features.map((feature, idx) => (
                  <div key={idx} className="flex items-center text-gray-300">
                    <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                    <span>{feature}</span>
                  </div>
                ))}
                
                <Button className={`w-full mt-8 bg-gradient-to-r ${plan.color} hover:opacity-90 text-white font-semibold py-3 rounded-xl transition-all duration-300`}>
                  {plan.price === "Custom" ? "Contact Sales" : "License Strategy"}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-16">
          <p className="text-gray-400 text-lg mb-4">
            All licenses include comprehensive due diligence documentation and compliance support.
          </p>
          <p className="text-sm text-gray-500">
            Pricing subject to strategy complexity and exclusivity terms. Contact our team for detailed proposals.
          </p>
        </div>
      </div>
    </section>
  );
};
