
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Crown, Star, Zap } from "lucide-react";
import { cn } from "@/lib/utils";

export const Pricing = () => {
  const plans = [
    {
      name: "Elite",
      price: "$150,000",
      originalPrice: "$250,000",
      period: "per strategy",
      description: "Most popular choice for serious institutions",
      icon: Crown,
      color: "from-purple-500 to-pink-500",
      features: [
        "Access to ALL Tier 1 & 2 strategies",
        "Dedicated implementation team",
        "Weekly performance analysis",
        "Priority phone & email support",
        "Custom parameter tuning",
        "Advanced documentation suite",
        "2-year license term",
        "Risk management consultation",
        "Strategy combination guidance"
      ],
      popular: true,
      savings: "$100,000",
      roi: "340% average ROI",
      guarantee: "30-day money-back guarantee",
      urgency: "Only 8 spots left",
      value: "$500,000+"
    },
    {
      name: "Select",
      price: "$75,000",
      originalPrice: "$100,000",
      period: "per strategy",
      description: "Perfect for emerging funds ready to scale",
      icon: Star,
      color: "from-blue-500 to-cyan-500",
      features: [
        "Access to Tier 2 strategies",
        "Implementation support included",
        "Monthly performance reports",
        "Email & chat support",
        "1-year license term",
        "Basic parameter customization",
        "Community forum access"
      ],
      popular: false,
      savings: "$25,000",
      roi: "195% average ROI",
      guarantee: "14-day trial period",
      urgency: "Limited time offer",
      value: "$200,000+"
    },
    {
      name: "Sovereign",
      price: "Custom",
      originalPrice: "Starting $500K",
      period: "enterprise pricing",
      description: "Exclusive partnerships for market leaders",
      icon: Zap,
      color: "from-gold-400 to-yellow-500",
      features: [
        "Exclusive strategy development",
        "Full white-label licensing",
        "Dedicated research team",
        "24/7 premium support",
        "Real-time performance monitoring",
        "Unlimited license term",
        "IP co-development rights",
        "Regulatory compliance support",
        "Executive advisory access"
      ],
      popular: false,
      savings: "Negotiable",
      roi: "Custom ROI targets",
      guarantee: "Performance guarantee",
      urgency: "By invitation only",
      value: "$2M+"
    }
  ];

  return (
    <section className="py-24 px-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-red-500/10 border border-red-500/20 px-4 py-2 rounded-full mb-6">
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
            <span className="text-red-400 font-semibold text-sm">LIMITED TIME: 40% OFF ALL LICENSES</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Secure Your <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">Competitive Edge</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-4">
            While your competitors struggle with 12% returns, our clients average 340% ROI.
          </p>
          <p className="text-lg text-red-400 font-semibold">
            ⚠️ Only 12 exclusive licenses remaining this quarter
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={cn(
                "group glass-subtle border-white/8 hover:border-white/15 transition-all duration-500 hover-lift hover-glow relative animate-scale-in",
                plan.popular ? 'ring-1 ring-purple-500/30 shadow-glow-subtle' : ''
              )}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-green-500 via-emerald-500 to-green-500 text-white px-5 py-2 rounded-full font-bold text-xs shadow-glow">
                    🔥 BEST VALUE - SAVE {plan.savings}
                  </div>
                </div>
              )}

              {/* Urgency Badge */}
              <div className="absolute -top-1 -right-1 z-10">
                <div className="bg-red-500 text-white px-3 py-1 rounded-full font-bold text-xs">
                  {plan.urgency}
                </div>
              </div>

              <CardHeader className="text-center pb-8 relative">
                <div className={`w-18 h-18 mx-auto mb-6 rounded-3xl bg-gradient-to-br ${plan.color} flex items-center justify-center shadow-glow-subtle group-hover:scale-105 transition-transform duration-300 relative`}>
                  <plan.icon className="w-9 h-9 text-white group-hover:animate-pulse-subtle" />
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <CardTitle className="text-3xl font-black text-white mb-3 group-hover:text-gradient-blue transition-all duration-300">
                  {plan.name}
                </CardTitle>
                <CardDescription className="text-gray-400 mb-6 group-hover:text-gray-300 transition-colors duration-300">
                  {plan.description}
                </CardDescription>

                {/* Value Stacking */}
                <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-xl p-4 mb-6">
                  <div className="text-center">
                    <div className="text-sm text-gray-400 mb-1">Total Value</div>
                    <div className="text-2xl font-bold text-green-400">{plan.value}</div>
                    <div className="text-sm text-green-400">{plan.roi}</div>
                  </div>
                </div>

                <div className="text-center">
                  {plan.originalPrice !== plan.price && (
                    <div className="text-lg text-gray-500 line-through mb-1">{plan.originalPrice}</div>
                  )}
                  <span className="text-5xl font-black text-gradient-blue">{plan.price}</span>
                  <span className="text-gray-400 text-lg ml-2 block mt-1">{plan.period}</span>
                  {plan.savings && plan.savings !== "Negotiable" && (
                    <div className="text-green-400 font-bold text-sm mt-2">
                      💰 You Save {plan.savings}
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {plan.features.map((feature, idx) => (
                    <div
                      key={idx}
                      className="flex items-start text-gray-300 group-hover:text-gray-200 transition-colors duration-300 animate-slide-up"
                      style={{ animationDelay: `${(index * 0.2) + (idx * 0.1)}s` }}
                    >
                      <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0 mt-0.5 group-hover:text-green-300 transition-colors duration-300" />
                      <span className="leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Guarantee */}
                <div className="text-center py-4 border-t border-white/10">
                  <div className="text-sm text-green-400 font-semibold">
                    ✅ {plan.guarantee}
                  </div>
                </div>

                <div className="pt-2">
                  <Button className={cn(
                    "w-full bg-gradient-to-r font-bold py-4 text-lg rounded-2xl transition-all duration-300 hover:scale-105 shadow-glow group",
                    plan.popular
                      ? "bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:shadow-glow"
                      : `bg-gradient-to-r ${plan.color} text-white hover:shadow-glow-purple`
                  )}>
                    <span className="relative z-10">
                      {plan.price === "Custom" ? "🚀 Apply for Sovereign Access" : "🔒 Secure License Now"}
                    </span>
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Button>

                  {/* Payment Options */}
                  {plan.price !== "Custom" && (
                    <div className="text-center mt-3">
                      <div className="text-xs text-gray-400">
                        💳 Payment plans available • 🔒 Secure checkout
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <p className="text-gray-400 text-lg mb-4">
            All licenses include comprehensive due diligence documentation and compliance support.
          </p>
          <p className="text-sm text-gray-500">
            Pricing subject to strategy complexity and exclusivity terms. Contact our team for detailed proposals.
          </p>
        </div>
      </div>
    </section>
  );
};
