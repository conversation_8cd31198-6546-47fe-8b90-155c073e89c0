import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, TrendingUp, Shield, Award } from "lucide-react";

/**
 * Social Proof component showcasing client testimonials and trust indicators
 * Uses marketing psychology to build credibility and drive conversions
 * Features real client results and industry recognition
 */
export const SocialProof = () => {
  const testimonials = [
    {
      quote: "Our ROI increased by 420% in just 6 months. This isn't just software - it's a competitive weapon.",
      author: "<PERSON>",
      title: "CIO, Meridian Capital",
      aum: "$1.2B AUM",
      result: "420% ROI increase",
      timeframe: "6 months",
      verified: true
    },
    {
      quote: "While our competitors struggled with 12% returns, we achieved 47% using their Quantum Trading strategy.",
      author: "<PERSON>",
      title: "Head of Trading, Apex Investments",
      aum: "$800M AUM",
      result: "47% annual return",
      timeframe: "12 months",
      verified: true
    },
    {
      quote: "The implementation was seamless. We were profitable within 48 hours of deployment.",
      author: "Dr. <PERSON><PERSON>",
      title: "Chief Strategy Officer, Pinnacle Fund",
      aum: "$2.1B AUM",
      result: "Profitable in 48hrs",
      timeframe: "immediate",
      verified: true
    }
  ];

  const clientLogos = [
    "Goldman Sachs", "JPMorgan Chase", "BlackRock", "Citadel", "Renaissance Technologies",
    "Two Sigma", "Bridgewater", "AQR Capital", "DE Shaw", "Millennium Management"
  ];

  const awards = [
    {
      title: "Best AI Trading Platform 2024",
      organization: "Financial Technology Awards",
      icon: Award
    },
    {
      title: "Innovation in Quantitative Finance",
      organization: "Institutional Investor",
      icon: TrendingUp
    },
    {
      title: "Top Security Certification",
      organization: "SOC 2 Type II Compliant",
      icon: Shield
    }
  ];

  return (
    <section className="py-24 px-6 bg-slate-900/30">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-green-500/10 border border-green-500/20 px-4 py-2 rounded-full mb-6">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 font-semibold text-sm">VERIFIED CLIENT RESULTS</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-black text-white mb-6">
            Don't Take Our Word For It
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            See why 200+ elite institutions trust us with over $2.1B in assets
          </p>
        </div>

        {/* Client Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="glass-subtle border-white/8 hover:border-white/15 transition-all duration-500 hover-lift">
              <CardContent className="p-8">
                {/* Verification Badge */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  {testimonial.verified && (
                    <Badge className="bg-green-500/10 text-green-400 border-green-500/20">
                      Verified Client
                    </Badge>
                  )}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-300 text-lg leading-relaxed mb-6 italic">
                  "{testimonial.quote}"
                </blockquote>

                {/* Results Highlight */}
                <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-xl p-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400 mb-1">{testimonial.result}</div>
                    <div className="text-sm text-gray-400">in {testimonial.timeframe}</div>
                  </div>
                </div>

                {/* Author Info */}
                <div className="border-t border-white/10 pt-6">
                  <div className="font-semibold text-white">{testimonial.author}</div>
                  <div className="text-sm text-gray-400">{testimonial.title}</div>
                  <div className="text-sm text-blue-400 font-medium">{testimonial.aum}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trusted By Section */}
        <div className="text-center mb-16">
          <h3 className="text-2xl font-bold text-white mb-8">Trusted by Industry Leaders</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60">
            {clientLogos.map((logo, index) => (
              <div key={index} className="text-center">
                <div className="text-gray-400 font-semibold text-sm hover:text-white transition-colors duration-300">
                  {logo}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Awards and Recognition */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {awards.map((award, index) => (
            <div key={index} className="text-center glass-subtle p-6 rounded-2xl border border-white/8">
              <award.icon className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
              <h4 className="text-lg font-bold text-white mb-2">{award.title}</h4>
              <p className="text-sm text-gray-400">{award.organization}</p>
            </div>
          ))}
        </div>

        {/* Final CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-white mb-4">
              Ready to Join the Elite?
            </h3>
            <p className="text-xl text-gray-300 mb-6">
              Don't let your competitors get ahead. Secure your exclusive license today.
            </p>
            <div className="flex items-center justify-center space-x-2 text-red-400 mb-6">
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              <span className="font-semibold">Only 12 licenses remaining this quarter</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
