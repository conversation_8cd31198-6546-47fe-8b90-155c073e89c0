import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, Shield, TrendingUp, Clock, Users } from "lucide-react";

/**
 * FAQ component designed to handle objections and drive conversions
 * Uses marketing psychology to address common concerns and build trust
 * Features expandable questions with detailed, benefit-focused answers
 */
export const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "How quickly will I see returns on my investment?",
      answer: "Most clients see positive returns within the first week of deployment. Our average client achieves 340% ROI within 12 months, with some seeing 400%+ returns in just 6 months. We provide real-time performance tracking so you can monitor your returns daily.",
      icon: TrendingUp,
      highlight: "Returns start within 7 days"
    },
    {
      question: "What if the strategies don't work for my specific market?",
      answer: "We offer a 30-day money-back guarantee because we're confident in our results. Our strategies are tested across 50+ different market conditions and asset classes. Plus, our dedicated team will customize parameters specifically for your market and risk profile at no extra cost.",
      icon: Shield,
      highlight: "30-day money-back guarantee"
    },
    {
      question: "How complex is the implementation process?",
      answer: "Implementation takes just 48 hours with our white-glove setup service. Our technical team handles everything - integration, testing, and optimization. You don't need any technical expertise. We also provide 24/7 support during the first month to ensure smooth operation.",
      icon: Clock,
      highlight: "48-hour setup included"
    },
    {
      question: "Why are you limiting access to only 200 institutions?",
      answer: "Exclusivity is key to maintaining competitive advantage. If everyone had access to these strategies, they would lose their edge. By limiting licenses, we ensure our clients maintain superior performance. This scarcity also allows us to provide personalized support to each client.",
      icon: Users,
      highlight: "Exclusive competitive advantage"
    },
    {
      question: "What makes your AI strategies better than others?",
      answer: "Our strategies are developed by former Goldman Sachs and Renaissance Technologies researchers with 50+ patents. Unlike generic solutions, ours adapt in real-time to market conditions. We've processed over $2.1B in assets with zero security breaches and maintain 99.9% uptime.",
      icon: Shield,
      highlight: "Proven by $2.1B+ in assets"
    },
    {
      question: "Can I try before committing to the full license?",
      answer: "Yes! We offer a 14-day trial period for Select tier and 30-day money-back guarantee for Elite tier. You can also schedule a free ROI analysis where we'll show you exactly how much additional profit you could generate with our strategies using your historical data.",
      icon: TrendingUp,
      highlight: "Risk-free trial available"
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-24 px-6">
      <div className="max-w-4xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-blue-500/10 border border-blue-500/20 px-4 py-2 rounded-full mb-6">
            <Shield className="w-4 h-4 text-blue-400" />
            <span className="text-blue-400 font-semibold text-sm">OBJECTION HANDLING</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-black text-white mb-6">
            Still Have <span className="text-gradient-blue">Questions?</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            We've answered the most common concerns from institutional investors
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="glass-subtle border-white/8 hover:border-white/15 transition-all duration-300">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-300"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <faq.icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-1">{faq.question}</h3>
                      <div className="text-sm text-green-400 font-medium">{faq.highlight}</div>
                    </div>
                  </div>
                  <div className="flex-shrink-0 ml-4">
                    {openIndex === index ? (
                      <ChevronUp className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </button>
                
                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <div className="pl-14">
                      <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Final CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Join the Elite 200?
            </h3>
            <p className="text-gray-300 mb-6">
              Don't let analysis paralysis cost you millions in potential profits.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 font-bold rounded-2xl hover:scale-105 transition-all duration-300 shadow-glow animate-pulse">
                🔒 Secure Your License Now
              </Button>
              <Button className="glass-subtle text-white border border-white/20 px-8 py-3 font-semibold rounded-2xl hover:scale-105 transition-all duration-300">
                📞 Schedule Free Consultation
              </Button>
            </div>
            <div className="mt-4 text-sm text-red-400 font-semibold">
              ⚠️ Only 12 licenses remaining this quarter
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
