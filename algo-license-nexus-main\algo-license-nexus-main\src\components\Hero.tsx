import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollIndicator } from "@/components/ScrollIndicator";
import { ArrowRight, Brain, Shield, Zap, TrendingUp, Star, Sparkles } from "lucide-react";

/**
 * Modern Hero component with enhanced animations and interactions
 * Features dynamic background effects, improved typography, and better CTAs
 * Includes floating elements and modern glassmorphism design
 */
export const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const features = [
    {
      icon: Brain,
      title: "Proprietary Algorithms",
      description: "Cutting-edge AI strategies not available anywhere else",
      color: "text-blue-400",
      gradient: "from-blue-500/20 to-cyan-500/20"
    },
    {
      icon: Shield,
      title: "Exclusive Licensing",
      description: "Limited availability ensures competitive advantage",
      color: "text-purple-400",
      gradient: "from-purple-500/20 to-pink-500/20"
    },
    {
      icon: Zap,
      title: "Proven Results",
      description: "Validated performance across multiple industries",
      color: "text-yellow-400",
      gradient: "from-yellow-500/20 to-orange-500/20"
    }
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center px-6 overflow-hidden">
      {/* Refined Background Animation */}
      <div className="absolute inset-0">
        {/* Subtle gradient orbs */}
        <div
          className="absolute w-[800px] h-[800px] bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-full mix-blend-multiply filter blur-3xl animate-float-slow opacity-60"
          style={{
            left: `${15 + mousePosition.x * 0.01}%`,
            top: `${5 + mousePosition.y * 0.01}%`,
          }}
        />
        <div
          className="absolute w-[600px] h-[600px] bg-gradient-to-r from-purple-500/12 to-pink-500/12 rounded-full mix-blend-multiply filter blur-3xl animate-drift opacity-50"
          style={{
            right: `${10 + mousePosition.x * 0.008}%`,
            bottom: `${15 + mousePosition.y * 0.008}%`,
          }}
        />
        <div
          className="absolute w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-breathe opacity-40"
          style={{
            left: `${50 + mousePosition.x * 0.005}%`,
            top: `${50 + mousePosition.y * 0.005}%`,
          }}
        />

        {/* Refined floating particles */}
        <div className="absolute inset-0">
          {[...Array(12)].map((_, i) => (
            <div
              key={i}
              className="absolute w-0.5 h-0.5 bg-white/15 rounded-full animate-pulse-subtle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 4}s`,
                animationDuration: `${6 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>
      </div>

      <div className="max-w-7xl mx-auto text-center relative z-10 animate-fade-in">
        {/* Refined Premium Badge */}
        <div className="flex justify-center mb-12 animate-slide-up">
          <div className="glass-subtle px-8 py-4 rounded-full border border-white/10 hover-glow group">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4 text-yellow-400 animate-pulse-subtle" />
                <Shield className="w-4 h-4 text-blue-400" />
              </div>
              <span className="text-sm font-semibold text-white letter-spacing-wide">
                EXCLUSIVE AI STRATEGIES
              </span>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-yellow-400 fill-current opacity-80" />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Refined Main Heading */}
        <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-10 leading-none animate-slide-up letter-spacing-tight">
          <span className="block text-balance">License</span>
          <span className="block text-gradient-blue animate-glow-subtle">Elite AI</span>
          <span className="block text-balance">Strategies</span>
        </h1>

        {/* Refined Subheading */}
        <p className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-16 max-w-4xl mx-auto leading-relaxed font-light animate-slide-up text-balance">
          Access <span className="text-blue-400 font-medium">proprietary algorithmic strategies</span> developed by world-class AI researchers.
          Transform your business with <span className="text-purple-400 font-medium">exclusive, battle-tested algorithms</span> that drive unprecedented results.
        </p>

        {/* Refined CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-20 animate-slide-up">
          <Link to="/services">
            <Button className="group bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-size-200 bg-pos-0 hover:bg-pos-100 text-white px-12 py-4 text-lg font-semibold rounded-2xl transition-all duration-500 hover-lift shadow-glow-subtle hover:shadow-glow">
              <TrendingUp className="mr-3 w-5 h-5 group-hover:rotate-6 transition-transform duration-300" />
              Explore Strategies
              <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-0.5 transition-transform duration-300" />
            </Button>
          </Link>
          <Link to="/contact">
            <Button className="group glass-subtle text-white hover:bg-white/8 border border-white/20 hover:border-white/30 px-12 py-4 text-lg font-semibold rounded-2xl transition-all duration-300 hover-lift">
              Schedule Consultation
              <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-0.5 transition-transform duration-300" />
            </Button>
          </Link>
        </div>

        {/* Refined Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto animate-slide-up">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group glass-subtle p-8 rounded-3xl border border-white/8 hover:border-white/15 transition-all duration-500 hover-lift hover-glow animate-scale-in"
              style={{ animationDelay: `${index * 0.15}s` }}
            >
              <div className={`w-14 h-14 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center group-hover:scale-105 transition-transform duration-300`}>
                <feature.icon className={`w-7 h-7 ${feature.color} group-hover:animate-pulse-subtle`} />
              </div>
              <h3 className="text-lg font-bold text-white mb-4 group-hover:text-blue-400 transition-colors duration-300 text-balance">
                {feature.title}
              </h3>
              <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300 text-balance">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Refined Performance Stats */}
        <div className="mt-24 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto animate-slide-up">
          {[
            { value: "340%", label: "Average ROI" },
            { value: "200+", label: "Global Clients" },
            { value: "50+", label: "AI Patents" },
            { value: "99.9%", label: "Uptime" }
          ].map((stat, index) => (
            <div key={index} className="text-center group hover-lift">
              <div className="text-3xl md:text-4xl font-black text-gradient-blue mb-3 group-hover:scale-105 transition-transform duration-300 letter-spacing-tight">
                {stat.value}
              </div>
              <div className="text-xs text-gray-400 font-semibold uppercase tracking-wider letter-spacing-wide">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>

      <ScrollIndicator />
    </section>
  );
};
