import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Brain, Shield, Zap, TrendingUp, Star, Sparkles } from "lucide-react";

/**
 * Modern Hero component with enhanced animations and interactions
 * Features dynamic background effects, improved typography, and better CTAs
 * Includes floating elements and modern glassmorphism design
 */
export const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const features = [
    {
      icon: Brain,
      title: "Proprietary Algorithms",
      description: "Cutting-edge AI strategies not available anywhere else",
      color: "text-blue-400",
      gradient: "from-blue-500/20 to-cyan-500/20"
    },
    {
      icon: Shield,
      title: "Exclusive Licensing",
      description: "Limited availability ensures competitive advantage",
      color: "text-purple-400",
      gradient: "from-purple-500/20 to-pink-500/20"
    },
    {
      icon: Zap,
      title: "Proven Results",
      description: "Validated performance across multiple industries",
      color: "text-yellow-400",
      gradient: "from-yellow-500/20 to-orange-500/20"
    }
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center px-6 overflow-hidden">
      {/* Enhanced Background Animation */}
      <div className="absolute inset-0">
        {/* Dynamic gradient orbs */}
        <div
          className="absolute w-[600px] h-[600px] bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-full mix-blend-multiply filter blur-3xl animate-float opacity-70"
          style={{
            left: `${20 + mousePosition.x * 0.02}%`,
            top: `${10 + mousePosition.y * 0.02}%`,
          }}
        />
        <div
          className="absolute w-[500px] h-[500px] bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-full mix-blend-multiply filter blur-3xl animate-float opacity-70"
          style={{
            right: `${15 + mousePosition.x * 0.015}%`,
            bottom: `${20 + mousePosition.y * 0.015}%`,
            animationDelay: '2s'
          }}
        />
        <div
          className="absolute w-[400px] h-[400px] bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full mix-blend-multiply filter blur-3xl animate-float opacity-60"
          style={{
            left: `${60 + mousePosition.x * 0.01}%`,
            top: `${60 + mousePosition.y * 0.01}%`,
            animationDelay: '4s'
          }}
        />

        {/* Floating particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 6}s`,
                animationDuration: `${4 + Math.random() * 4}s`
              }}
            />
          ))}
        </div>
      </div>

      <div className="max-w-7xl mx-auto text-center relative z-10 animate-fade-in">
        {/* Premium Badge */}
        <div className="flex justify-center mb-8 animate-slide-up">
          <div className="glass-strong px-6 py-3 rounded-full border border-white/20 shadow-glow">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1">
                <Sparkles className="w-4 h-4 text-yellow-400 animate-pulse" />
                <Shield className="w-4 h-4 text-blue-400" />
              </div>
              <span className="text-sm font-medium text-white">Exclusive AI Strategies</span>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Heading */}
        <h1 className="text-6xl md:text-8xl lg:text-9xl font-black text-white mb-8 leading-none animate-slide-up">
          <span className="block">License</span>
          <span className="block text-gradient-blue animate-glow">Elite AI</span>
          <span className="block">Strategies</span>
        </h1>

        {/* Subheading */}
        <p className="text-xl md:text-2xl lg:text-3xl text-gray-300 mb-12 max-w-5xl mx-auto leading-relaxed font-light animate-slide-up">
          Access <span className="text-blue-400 font-semibold">proprietary algorithmic strategies</span> developed by world-class AI researchers.
          Transform your business with <span className="text-purple-400 font-semibold">exclusive, battle-tested algorithms</span> that drive unprecedented results.
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16 animate-slide-up">
          <Link to="/services">
            <Button className="group bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-size-200 bg-pos-0 hover:bg-pos-100 text-white px-10 py-5 text-lg font-semibold rounded-2xl transition-all duration-500 hover:scale-105 shadow-glow hover:shadow-glow-purple animate-glow">
              <TrendingUp className="mr-3 w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
              Explore Strategies
              <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
          </Link>
          <Link to="/contact">
            <Button className="group glass-strong text-white hover:bg-white/10 border-2 border-white/30 hover:border-white/50 px-10 py-5 text-lg font-semibold rounded-2xl transition-all duration-300 hover:scale-105 shadow-modern-lg">
              Schedule Consultation
              <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
          </Link>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto animate-slide-up">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group glass-strong p-8 rounded-3xl border border-white/10 hover:border-white/20 transition-all duration-500 hover:scale-105 hover:shadow-modern-lg animate-scale-in"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className={`w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className={`w-8 h-8 ${feature.color} group-hover:animate-pulse`} />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors duration-300">
                {feature.title}
              </h3>
              <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Performance Stats */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto animate-slide-up">
          {[
            { value: "340%", label: "Average ROI" },
            { value: "200+", label: "Global Clients" },
            { value: "50+", label: "AI Patents" },
            { value: "99.9%", label: "Uptime" }
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="text-3xl md:text-4xl font-black text-gradient-blue mb-2 group-hover:scale-110 transition-transform duration-300">
                {stat.value}
              </div>
              <div className="text-sm text-gray-400 font-medium uppercase tracking-wider">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
