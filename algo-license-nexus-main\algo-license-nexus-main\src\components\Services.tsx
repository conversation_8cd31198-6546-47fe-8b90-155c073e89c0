
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Target, Database, Cpu } from "lucide-react";

export const Services = () => {
  const strategies = [
    {
      icon: TrendingUp,
      title: "Quantum Trading Algorithms",
      description: "Revolutionary quantum-inspired trading strategies with 340% average ROI",
      features: ["Real-time market analysis", "Risk optimization", "Predictive modeling"],
      exclusivity: "Tier 1",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Target,
      title: "Neural Prediction Engine",
      description: "Advanced neural networks for market forecasting and trend prediction",
      features: ["Deep learning models", "Pattern recognition", "Sentiment analysis"],
      exclusivity: "Tier 1",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Database,
      title: "Adaptive Portfolio Optimizer",
      description: "Self-learning portfolio management with dynamic risk adjustment",
      features: ["Portfolio balancing", "Risk assessment", "Performance tracking"],
      exclusivity: "Tier 2",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Cpu,
      title: "Multi-Asset Strategy Suite",
      description: "Comprehensive algorithmic strategies for diverse asset classes",
      features: ["Cross-market analysis", "Arbitrage detection", "Liquidity optimization"],
      exclusivity: "Tier 2",
      color: "from-orange-500 to-red-500"
    }
  ];

  return (
    <section className="py-24 px-6 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Premium AI <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Strategy Portfolio</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our exclusive algorithmic strategies are the result of years of research and development by leading AI scientists and quantitative analysts.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {strategies.map((strategy, index) => (
            <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 group">
              <CardHeader>
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${strategy.color} bg-opacity-20`}>
                    <strategy.icon className="w-8 h-8 text-white" />
                  </div>
                  <Badge variant="secondary" className="bg-gradient-to-r from-gold-400 to-yellow-500 text-black font-semibold">
                    {strategy.exclusivity}
                  </Badge>
                </div>
                <CardTitle className="text-2xl text-white group-hover:text-blue-400 transition-colors">
                  {strategy.title}
                </CardTitle>
                <CardDescription className="text-gray-300 text-lg">
                  {strategy.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {strategy.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-400">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-16">
          <p className="text-gray-400 text-lg">
            All strategies include comprehensive documentation, implementation support, and ongoing updates.
          </p>
        </div>
      </div>
    </section>
  );
};
