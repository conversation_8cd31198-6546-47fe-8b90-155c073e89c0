
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Target, Database, Cpu } from "lucide-react";

export const Services = () => {
  const strategies = [
    {
      icon: TrendingUp,
      title: "Quantum Trading Algorithms",
      description: "Revolutionary quantum-inspired trading strategies with 340% average ROI",
      features: ["Real-time market analysis", "Risk optimization", "Predictive modeling"],
      exclusivity: "Tier 1",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Target,
      title: "Neural Prediction Engine",
      description: "Advanced neural networks for market forecasting and trend prediction",
      features: ["Deep learning models", "Pattern recognition", "Sentiment analysis"],
      exclusivity: "Tier 1",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Database,
      title: "Adaptive Portfolio Optimizer",
      description: "Self-learning portfolio management with dynamic risk adjustment",
      features: ["Portfolio balancing", "Risk assessment", "Performance tracking"],
      exclusivity: "Tier 2",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Cpu,
      title: "Multi-Asset Strategy Suite",
      description: "Comprehensive algorithmic strategies for diverse asset classes",
      features: ["Cross-market analysis", "Arbitrage detection", "Liquidity optimization"],
      exclusivity: "Tier 2",
      color: "from-orange-500 to-red-500"
    }
  ];

  return (
    <section className="py-24 px-6 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Premium AI <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Strategy Portfolio</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our exclusive algorithmic strategies are the result of years of research and development by leading AI scientists and quantitative analysts.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {strategies.map((strategy, index) => (
            <Card
              key={index}
              className="group glass-strong border-white/10 hover:border-white/20 transition-all duration-500 hover:scale-105 hover:shadow-modern-lg animate-scale-in"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <CardHeader className="relative">
                <div className="flex items-center justify-between mb-6">
                  <div className={`relative p-4 rounded-2xl bg-gradient-to-br ${strategy.color} shadow-glow group-hover:scale-110 transition-transform duration-300`}>
                    <strategy.icon className="w-10 h-10 text-white group-hover:animate-pulse" />
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold px-3 py-1 rounded-full shadow-glow">
                    {strategy.exclusivity}
                  </Badge>
                </div>
                <CardTitle className="text-2xl font-bold text-white group-hover:text-gradient-blue transition-all duration-300 mb-3">
                  {strategy.title}
                </CardTitle>
                <CardDescription className="text-gray-300 text-lg leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                  {strategy.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {strategy.features.map((feature, idx) => (
                    <li
                      key={idx}
                      className="flex items-start text-gray-400 group-hover:text-gray-300 transition-colors duration-300 animate-slide-up"
                      style={{ animationDelay: `${(index * 0.2) + (idx * 0.1)}s` }}
                    >
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mr-3 mt-2 flex-shrink-0 group-hover:shadow-glow transition-all duration-300"></div>
                      <span className="leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Performance indicator */}
                <div className="mt-6 p-4 glass rounded-xl border border-white/5">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Performance Rating</span>
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={i}
                          className={`w-2 h-2 rounded-full ${i < 4 ? 'bg-gradient-to-r from-blue-400 to-purple-400' : 'bg-gray-600'} group-hover:shadow-glow transition-all duration-300`}
                          style={{ animationDelay: `${i * 0.1}s` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <p className="text-gray-400 text-lg">
            All strategies include comprehensive documentation, implementation support, and ongoing updates.
          </p>
        </div>
      </div>
    </section>
  );
};
