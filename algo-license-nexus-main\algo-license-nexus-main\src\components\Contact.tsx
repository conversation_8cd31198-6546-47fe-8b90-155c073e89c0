import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mail, Phone, MapPin, Clock } from "lucide-react";

export const Contact = () => {
  return (
    <section className="py-24 px-6 bg-gradient-to-r from-slate-900/50 to-blue-900/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-green-500/10 border border-green-500/20 px-4 py-2 rounded-full mb-6">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 font-semibold text-sm">FREE ROI ANALYSIS AVAILABLE</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Claim Your <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">Competitive Edge</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-4">
            Get a free analysis showing exactly how much additional profit you could generate with our AI strategies.
          </p>
          <div className="glass-strong border border-red-500/30 rounded-xl p-4 max-w-2xl mx-auto bg-red-500/15">
            <p className="text-red-300 font-bold">
              ⚠️ WARNING: Only 12 exclusive licenses remaining this quarter.
              <br />Don't let your competitors secure the last spots.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="bg-white/5 backdrop-blur-sm border-white/10">
            <CardHeader>
              <CardTitle className="text-2xl text-white">🚀 Get Your FREE ROI Analysis</CardTitle>
              <CardDescription className="text-gray-400">
                See exactly how much profit you're leaving on the table. Our specialists respond within 2 hours.
              </CardDescription>
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 mt-4">
                <p className="text-green-400 font-semibold text-sm">
                  ✅ No obligation • ✅ Completely confidential • ✅ Custom analysis for your portfolio
                </p>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-gray-300">First Name</Label>
                  <Input id="firstName" placeholder="John" className="bg-white/10 border-white/20 text-white placeholder:text-gray-500" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-gray-300">Last Name</Label>
                  <Input id="lastName" placeholder="Doe" className="bg-white/10 border-white/20 text-white placeholder:text-gray-500" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-300">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" className="bg-white/10 border-white/20 text-white placeholder:text-gray-500" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company" className="text-gray-300">Company</Label>
                <Input id="company" placeholder="Your Investment Firm" className="bg-white/10 border-white/20 text-white placeholder:text-gray-500" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="interest" className="text-gray-300">Primary Interest</Label>
                <Select>
                  <SelectTrigger className="bg-white/10 border-white/20 text-white">
                    <SelectValue placeholder="Select strategy type" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-white/20">
                    <SelectItem value="quantum-trading">Quantum Trading Algorithms</SelectItem>
                    <SelectItem value="neural-prediction">Neural Prediction Engine</SelectItem>
                    <SelectItem value="portfolio-optimizer">Adaptive Portfolio Optimizer</SelectItem>
                    <SelectItem value="multi-asset">Multi-Asset Strategy Suite</SelectItem>
                    <SelectItem value="custom">Custom Strategy Development</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message" className="text-gray-300">Message</Label>
                <Textarea
                  id="message"
                  placeholder="Tell us about your specific requirements and investment goals..."
                  rows={4}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-500"
                />
              </div>

              <Button className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 rounded-xl transition-all duration-300 hover:scale-105 shadow-glow">
                🔥 Get My FREE ROI Analysis Now
              </Button>
              <div className="text-center text-xs text-gray-300 mt-2 font-medium">
                💳 No payment required • 🔒 100% secure • ⚡ 2-hour response guarantee
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-8">
            <Card className="bg-white/5 backdrop-blur-sm border-white/10">
              <CardHeader>
                <CardTitle className="text-xl text-white">Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Email</div>
                    <div className="text-gray-400"><EMAIL></div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Phone</div>
                    <div className="text-gray-400">+****************</div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Office</div>
                    <div className="text-gray-400">Wall Street, New York, NY</div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Business Hours</div>
                    <div className="text-gray-400">Mon-Fri: 9AM-6PM EST</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/80 backdrop-blur-sm border-blue-500/30 relative z-10">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Confidentiality Guaranteed</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  All communications are protected by strict NDAs. Your strategic discussions and requirements
                  remain completely confidential throughout our engagement process.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};
